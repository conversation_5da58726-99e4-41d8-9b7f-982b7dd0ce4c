version: '3.8'

services:
  moodleapp:
    build:
      context: .
      dockerfile: Moodle
    image: moodle-web:1.0
    container_name: moodle-web
    ports:
      - 8086:80
    volumes:
      - moodledata:/var/www/moodledata
      - .:/var/www/html
      - ./lang:/var/www/moodledata/lang
    depends_on:
      - mariadb
      - redis
    environment:
      # Redis connection details
      REDIS_HOST: redis
      REDIS_PORT: 6379

  mariadb:
    image: mariadb:latest
    container_name: mariadb
    environment:
      MYSQL_DATABASE: moodle
      MYSQL_USER: moodle_user
      MYSQL_PASSWORD: moodle_password
      MYSQL_ROOT_PASSWORD: 123456
    volumes:
      - mariadb_data:/var/lib/mysql
    ports:
      - "1306:3306"

  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  moodledata:
  mariadb_data:
  redis_data:

