<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace communication_matrix\local\spec\features\synapse;

use communication_matrix\local\command;
use GuzzleHttp\Psr7\Response;

/**
 * Synapse API feature for fetching room info.
 *
 * https://matrix-org.github.io/synapse/latest/admin_api/rooms.html#room-details-api
 *
 * @package    communication_matrix
 * @copyright  2023 <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @codeCoverageIgnore
 * This code does not warrant being tested. Testing offers no discernible benefit given its usage is tested.
 */
trait get_room_info_v1 {
    /**
     * Get room info.
     *
     * @param string $roomid
     * @return Response
     */
    public function get_room_info(string $roomid): Response {
        return $this->execute(new command(
            $this,
            method: 'GET',
            endpoint: '_synapse/admin/v1/rooms/:roomid',
            params: [
                ':roomid' => $roomid,
            ],
        ));
    }
}
