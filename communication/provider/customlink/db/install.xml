<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="communication/provider/customlink/db" VERSION="20230826" COMMENT="Stores the link associated with a custom link communication instance."
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="communication_customlink" COMMENT="Stores the link associated with a custom link communication instance.">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="commid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID of the communication record"/>
        <FIELD NAME="url" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false" COMMENT="URL being linked to by the provider"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="fk_commid" TYPE="foreign" FIELDS="commid" REFTABLE="communication" REFFIELDS="id" COMMENT="Foreign key for communication reference"/>
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>
