<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Entry point for token-based access to pluginfile.php.
 *
 * @package    core
 * @copyright  2018 Andrew <PERSON>ls <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

// Disable the use of sessions/cookies - we recreate $USER for every call.
define('NO_MOODLE_COOKIES', true);

// Disable debugging for this script.
// It is typically used to display images.
define('NO_DEBUG_DISPLAY', true);

require_once('config.php');

// Allow CORS requests.
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: range');
header('Access-Control-Expose-Headers: Content-Range');

$relativepath = get_file_argument();
$token = optional_param('token', '', PARAM_ALPHANUM);
if (0 == strpos($relativepath, '/token/')) {
    $relativepath = ltrim($relativepath, '/');
    $pathparts = explode('/', $relativepath, 2);
    $token = $pathparts[0];
    $token = clean_param($token, PARAM_ALPHANUM);
    $relativepath = "/{$pathparts[1]}";
}

require_user_key_login('core_files', null, $token);
require_once('pluginfile.php');
