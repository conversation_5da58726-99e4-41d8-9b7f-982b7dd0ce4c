<?php
use local_examination\service\examination_shift_service;
use local_examination\task\send_notification_examination_feedback;

require_once(__DIR__ . '/../../../../config.php');
require_once($CFG->dirroot . '/local/examination/lib.php');
if (!confirm_sesskey()) {
    throw new \moodle_exception('confirmsesskeybad');
}

$function = required_param('function', PARAM_TEXT);

global $DB;

if ($function == 'update_shift_values_based_on_period') {

    $period_id = optional_param('periodid', 0, PARAM_INT);
    if (empty($period_id)) {
        echo json_encode(array('success' => false,'data' => []));
        exit;
    }

    $record = $DB->get_record('examination_period', ['id' => $period_id]);
    if (!$record) {
        echo json_encode(array('success' => false, 'data' => []));
        exit;
    }
    $data = array(
        'success' => true,
        'data' => array(
            'resulttype' => $record->resulttype,
            'attemptcount' => $record->attemptcount,
            'accesspassword' => $record->accesspassword,
            'reqscore' => $record->reqscore,
            'anticheating' => $record->anticheating,
            'showexitwarning' => $record->showexitwarning,
            'capturetime' => $record->capturetime,
            'iplimit' => $record->iplimit,
            'resultdisplaytype' => $record->resultdisplaytype,
            'showscore' => $record->showscore,
            'allowreview' => $record->allowreview,
            'showfeedback' => $record->showfeedback,
            'canselfenrol' => $record->canselfenrol,
            'visible' => $record->visible,
            'certificateid' => $record->certificateid,
            'certificationmethod' => $record->certificationmethod,
            'supporteddevices' => $record->supporteddevices ?? '',
            'showquestionexplanation' => $record->showquestionexplanation,
            'supportinformation' => $record->supportinformation,
            'notifyemail' => $record->notifyemail,
            'autosendemail' => $record->autosendemail,
            'displaytype' => $record->displaytype,
            'ispractice' => $record->ispractice,
            'notification' => $record->notification,
            'description' => $record->description,
            'exitalert' => $record->exitalert,
            'allowaudioplayback' => $record->allowaudioplayback,
            'autoplayaudio' => $record->autoplayaudio
        ),
    );
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}
if ($function == 'get_all_descendant_examination_period_categories') {
    $category_id = optional_param('category_id', null, PARAM_INT);
    $get_category_path = optional_param('get_category_path', 0, PARAM_INT);
    if (empty($category_id)){
        echo json_encode([]);
        exit;
    }
    function getCategoryPath($db, $category_id, $path = []) {
        $sql = "SELECT id, name, parent
                FROM {examination_period_categories} 
                WHERE id = :category_id";

        $category = $db->get_record_sql($sql, ['category_id' => $category_id]);

        if (!empty($category)) {
            array_unshift($path, [
                'id' => $category->id,
                'name' => $category->name,
                'parent' => $category->parent
            ]);

            if (!empty($category->parent)) {
                return getCategoryPath($db, $category->parent, $path);
            }
        }

        return $path;
    }

    $category_path = getCategoryPath($DB, $category_id);
    echo json_encode($category_path);
    exit;
}
if ($function == 'get_all_descendant_quiz_categories') {
    $category_id = optional_param('category_id', null, PARAM_INT);
    $get_category_path = optional_param('get_category_path', 0, PARAM_INT);
    if (empty($category_id)){
        echo json_encode([]);
        exit;
    }
    function getCategoryPath($db, $category_id, $path = []) {
        $sql = "SELECT id, name, parent
                FROM {quiz_categories} 
                WHERE id = :category_id";

        $category = $db->get_record_sql($sql, ['category_id' => $category_id]);

        if (!empty($category)) {
            array_unshift($path, [
                'id' => $category->id,
                'name' => $category->name,
                'parent' => $category->parent
            ]);

            if (!empty($category->parent)) {
                return getCategoryPath($db, $category->parent, $path);
            }
        }

        return $path;
    }

    $category_path = getCategoryPath($DB, $category_id);
    echo json_encode($category_path);
    exit;
}
if ($function == 'get_all_descendant_question_categories') {
    $category_id = optional_param('category_id', null, PARAM_INT);
    $get_category_path = optional_param('get_category_path', 0, PARAM_INT);
    if (empty($category_id)){
        echo json_encode([]);
        exit;
    }
    function getCategoryPath($db, $category_id, $path = []) {
        $sql = "SELECT id, name, parent
                FROM {question_categories} 
                WHERE id = :category_id";

        $category = $db->get_record_sql($sql, ['category_id' => $category_id]);

        if (!empty($category)) {
            array_unshift($path, [
                'id' => $category->id,
                'name' => $category->name,
                'parent' => $category->parent
            ]);

            if (!empty($category->parent)) {
                return getCategoryPath($db, $category->parent, $path);
            }
        }

        return $path;
    }

    $category_path = getCategoryPath($DB, $category_id);
    echo json_encode($category_path);
    exit;
}
if ($function == 'get_tree_question_categories') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);
        $exam_org_ids = get_user_managing_examination_org_ancestor();

        $sql = "SELECT c.* 
            FROM {question_categories} c
            JOIN {organization} o on o.id = c.organizationid
            WHERE c.contextid = 1 AND c.code IS NOT NULL AND c.status = 1";

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        } else {
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' ) ";
        }
        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql);
        $items = [];

        $user_question_category_ids = get_user_question_category_ids(true);
        foreach ($records as $record) {
            if (!in_array($record->id, $user_question_category_ids)) {
                continue;
            }
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {question_categories} c
                WHERE c.contextid = 1 AND c.code IS NOT NULL AND c.status = 1 AND c.parent = $record->id ");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
                'canInteract' => in_array($record->organizationid, $exam_org_ids) && $record->status == '1',
                'organizationId' => $record->organizationid,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_export_tree_question_categories') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $orgid = optional_param('parentInitial', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);
        $exam_org_ids = get_user_managing_examination_org_ancestor();
        $params = [];
        $sql = "SELECT c.* 
            FROM {question_categories} c
            LEFT JOIN {question_categories} cp ON cp.id = c.parent
            JOIN {organization} o on o.id = c.organizationid
            WHERE c.contextid = 1 AND c.code IS NOT NULL AND c.status = 1";

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        }else if (!empty($orgid)) {
            $sql .= " AND c.organizationid = :orgid AND (cp.id IS NULL OR cp.organizationid != :orgid1)";
            $params['orgid'] = $orgid;
            $params['orgid1'] = $orgid;
        }else {
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' ) ";
        }
        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql,$params);
        $items = [];

        $user_question_category_ids = get_user_question_category_ids(true);
        foreach ($records as $record) {
            if (!in_array($record->id, $user_question_category_ids)) {
                continue;
            }
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {question_categories} c
                WHERE c.contextid = 1 AND c.code IS NOT NULL AND c.status = 1 AND c.parent = $record->id ");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
                'canInteract' => in_array($record->organizationid, $exam_org_ids) && $record->status == '1',
                'organizationId' => $record->organizationid,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_tree_question_categories_in_question_bank_view') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $cmid = optional_param('cmid', 0, PARAM_INT);
        $orgid = optional_param('parentInitial', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);

        $sql = "SELECT c.* 
            FROM {question_categories} c
            JOIN {organization} o on o.id = c.organizationid
            LEFT JOIN {question_categories} cp ON cp.id = c.parent
            WHERE c.contextid = 1 AND c.code IS NOT NULL AND c.status = 1";
        if (!$isCapabilityForAllOrg) {
            $organization_ids = implode(',', get_user_managing_org_ancestor());
            if (!empty($organization_ids)) {
                $sql .= " AND o.id in ( $organization_ids ) ";
            } else {
                $sql .= " AND 1 = 0 ";
            }
        }
        if (!empty($orgid)) {
            $sql .= " AND c.organizationid = $orgid";
        }
        if (!empty($cmid)) {
            $cm_organization_ids = $DB->get_fieldset_sql("SELECT o.id
                FROM mdl_course_modules cm
                    JOIN {modules} m ON m.id = cm.module and m.name = 'quiz'
                    JOIN {quiz} q ON q.id = cm.instance
                    JOIN {organization} o1 ON o1.id = q.organizationid
                    JOIN {organization} o ON o.path like concat(o1.path, '%') AND o.is_edu_unit = 1 and o.status = 1
                where cm.course = 1 and cm.id = $cmid");
            if (!empty($cm_organization_ids)) {
                $cm_organization_ids = implode(',', $cm_organization_ids);
                $sql .= " AND o.id in ( $cm_organization_ids ) ";
            } else {
                $sql .= " AND 1 = 0 ";
            }
        }

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        } else {
            $ext_sql = !empty($cm_organization_ids) ? " OR (c.parent IS NOT NULL AND cp.organizationid NOT IN ($cm_organization_ids))" : '';
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' $ext_sql) ";
        }
        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql);
        $items = [];
        if (empty($parent_id) && !empty($record)) {
            $items[] = [
                'id' => 0,
                'label' => ' ',
                'hasChildren' => false,
            ];
        }
        foreach ($records as $record) {
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {question_categories} c
                WHERE c.contextid = 1 AND c.code IS NOT NULL AND c.status = 1 AND c.parent = $record->id ");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}
if ($function == 'get_tree_quiz_categories') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);
        $exam_org_ids = get_user_managing_examination_org_ancestor();

        $sql = "SELECT c.* 
            FROM {quiz_categories} c
            JOIN {organization} o on o.id = c.organizationid
            WHERE c.status = 1";

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        } else {
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' ) ";
        }
        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql);
        $items = [];
        if (empty($parent_id) && !empty($record)) {
            $items[] = [
                'id' => 0,
                'label' => ' ',
                'hasChildren' => false,
            ];
        }

        $user_quiz_category_ids = get_user_quiz_category_ids(true);
        foreach ($records as $record) {
            if (!in_array($record->id, $user_quiz_category_ids)) {
                continue;
            }
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {quiz_categories} c
                WHERE c.status = 1 AND c.parent = $record->id ");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
                'canInteract' => in_array($record->organizationid, $exam_org_ids) && $record->status == '1',
                'organizationId' => $record->organizationid,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_export_tree_quiz_categories') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);
        $orgid = optional_param('parentInitial', 0, PARAM_INT);
        $exam_org_ids = get_user_managing_examination_org_ancestor();
        $params = [];
        $sql = "SELECT c.* 
            FROM {quiz_categories} c
            LEFT JOIN {quiz_categories} cp ON cp.id = c.parent
            JOIN {organization} o on o.id = c.organizationid
            WHERE c.status = 1";

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        } else if (!empty($orgid)) {
            $sql .= " AND c.organizationid = :orgid AND (cp.id IS NULL OR cp.organizationid != :orgid1)";
            $params['orgid'] = $orgid;
            $params['orgid1'] = $orgid;
        } else {
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' ) ";
        }

        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql, $params);
        $items = [];
        if (empty($parent_id) && !empty($record)) {
            $items[] = [
                'id' => 0,
                'label' => ' ',
                'hasChildren' => false,
            ];
        }

        $user_quiz_category_ids = get_user_quiz_category_ids(true);
        foreach ($records as $record) {
            if (!in_array($record->id, $user_quiz_category_ids)) {
                continue;
            }
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {quiz_categories} c
                WHERE c.status = 1 AND c.parent = $record->id ");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
                'canInteract' => in_array($record->organizationid, $exam_org_ids) && $record->status == '1',
                'organizationId' => $record->organizationid,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_tree_examination_period_categories') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);
        $exam_org_ids = get_user_managing_examination_org_ancestor();
        $sql = "SELECT c.* 
            FROM {examination_period_categories} c
            JOIN {organization} o on o.id = c.organizationid
            WHERE c.status = 1";

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        } else {
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' ) ";
        }
        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql);
        $items = [];
        if (empty($parent_id) && !empty($record)) {
            $items[] = [
                'id' => 0,
                'label' => ' ',
                'hasChildren' => false,
            ];
        }

        $user_period_category_ids = get_user_period_category_ids(true);
        foreach ($records as $record) {
            if (!in_array($record->id, $user_period_category_ids)) {
                continue;
            }
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {examination_period_categories} c
                WHERE c.status = 1 AND c.parent = $record->id");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
                'canInteract' => in_array($record->organizationid, $exam_org_ids) && $record->status == '1',
                'organizationId' => $record->organizationid,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_export_tree_examination_period_categories') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $orgid = optional_param('parentInitial', 0, PARAM_INT);
        $isCapabilityForAllOrg = is_capability_for_all_org(null);
        $exam_org_ids = get_user_managing_examination_org_ancestor();
        $params = [];
        $sql = "SELECT c.* 
            FROM {examination_period_categories} c
            LEFT JOIN {examination_period_categories} cp ON cp.id = c.parent
            JOIN {organization} o on o.id = c.organizationid
            WHERE c.status = 1";

        if (!empty($parent_id)) {
            $sql .= " AND c.parent = $parent_id ";
        } else if (!empty($orgid)) {
            $sql .= " AND c.organizationid = :orgid AND (cp.id IS NULL OR cp.organizationid != :orgid1)";
            $params['orgid'] = $orgid;
            $params['orgid1'] = $orgid;
        } else {
            $sql .= " AND (c.parent = 0 OR c.parent IS NULL OR c.parent = '' ) ";
        }
        $sql .= " ORDER BY c.displayorder";

        $records = $DB->get_records_sql($sql,$params);
        $items = [];
        if (empty($parent_id) && !empty($record)) {
            $items[] = [
                'id' => 0,
                'label' => ' ',
                'hasChildren' => false,
            ];
        }

        $user_period_category_ids = get_user_period_category_ids(true);
        foreach ($records as $record) {
            if (!in_array($record->id, $user_period_category_ids)) {
                continue;
            }
            $has_children = $DB->record_exists_sql("SELECT c.id
                FROM {examination_period_categories} c
                WHERE c.status = 1 AND c.parent = $record->id");
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
                'canInteract' => in_array($record->organizationid, $exam_org_ids) && $record->status == '1',
                'organizationId' => $record->organizationid,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_tree_orgs') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $initial_id = optional_param('parentInitial', 0, PARAM_INT);
        $only_edu_unit = optional_param('onlyEduUnit', true, PARAM_BOOL);

        $isCapabilityForAllOrg = is_capability_for_all_org(null);

        $sql = " SELECT DISTINCT o.id, o.organization_id, o.name
            FROM {organization} o ";
        $whereCondition = " WHERE o.status = 1 ";
        if (!empty($only_edu_unit)) {
            $whereCondition .= " AND o.is_edu_unit = 1 ";
        }

        if (!empty($initial_id) && empty($parent_id)) {
            $whereCondition .= " AND o.id = $initial_id ";
        } else {
            if (!$isCapabilityForAllOrg && empty($parent_id)) {
                $sql .= " JOIN {role_org_assignment} roa on roa.organization_id = o.id ";
                $whereCondition .= " AND roa.user_id = $USER->id AND roa.organization_id IS NOT NULL AND roa.organization_id != 0 ";
            }

            if (!empty($parent_id)) {
                $current_parent = $DB->get_record('organization', ['id' => $parent_id]);
                $whereCondition .= " AND o.org_parent_id = $current_parent->organization_id ";
            } else {
                $accept_roles = [\local_examination\constants::ADMIN_ROLE, \local_examination\constants::QUESTIONBANK_ADMIN_ROLE, \local_examination\constants::QUIZ_ADMIN_ROLE, \local_examination\constants::EXAMINATION_PERIOD_ADMIN_ROLE, \local_examination\constants::COURSE_CREATOR_ROLE];

                if (!empty(optional_param('forQuestion', false, PARAM_BOOL))) {
                    $accept_roles = [\local_examination\constants::ADMIN_ROLE, \local_examination\constants::QUESTIONBANK_ADMIN_ROLE];
                } else if (!empty(optional_param('forQuiz', false, PARAM_BOOL))) {
                    $accept_roles = [\local_examination\constants::ADMIN_ROLE, \local_examination\constants::QUIZ_ADMIN_ROLE];
                } else if (!empty(optional_param('forPeriod', false, PARAM_BOOL))) {
                    $accept_roles = [\local_examination\constants::ADMIN_ROLE, \local_examination\constants::EXAMINATION_PERIOD_ADMIN_ROLE];
                }

                $shortnames = '\'' . implode('\',\'', $accept_roles) . '\'';
                $roa_organization_ids = $DB->get_fieldset_sql("select distinct roa.organization_id
                    from mdl_role_org_assignment roa
                    join mdl_role r on r.id = roa.role_id
                    where r.shortname in ($shortnames)
                    and roa.user_id = $USER->id");
                if ($isCapabilityForAllOrg) {
                    $whereCondition .= " AND (o.org_parent_id = 0 OR o.org_parent_id IS NULL OR o.org_parent_id = '' ) ";
                } else {
                    if (!empty($roa_organization_ids)) {
                        $whereCondition .= " AND o.id IN ( " . implode(',', $roa_organization_ids) . ") ";

                        $roa_organization_paths = $DB->get_fieldset_sql("select distinct o.path from {organization} o where o.id in ( " . implode(',', $roa_organization_ids) . ") ");
                        foreach ($roa_organization_paths as $roa_organization_path) {
                            $whereCondition .= " AND (o.path LIKE '$roa_organization_path' OR o.path NOT LIKE concat('$roa_organization_path', '%'))";
                        }
                    } else {
                        $whereCondition .= " AND 1 = 0 ";
                    }
                }
            }
        }

        $records = $DB->get_records_sql($sql . $whereCondition);
        $items = [];
        foreach ($records as $record) {
            if ($only_edu_unit) {
                $has_children = $DB->record_exists('organization', ['org_parent_id' => $record->organization_id, 'is_edu_unit' => 1, 'status' => 1]);
            } else {
                $has_children = $DB->record_exists('organization', ['org_parent_id' => $record->organization_id, 'status' => 1]);
            }
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_tree_org_and_descendants_for_questionbank') {
    try {
        global $USER, $DB;
        $parent_id = optional_param('parent', 0, PARAM_INT);
        $initial_id = optional_param('parentInitial', 0, PARAM_INT);
        $initial_item_id = null;

        if (!empty($initial_id) && empty($parent_id)) {
            $initial_item_id = $DB->get_field_sql("select q.organizationid
                from {course_modules} cm
                JOIN {modules} m ON m.id = cm.module and m.name = 'quiz'
                JOIN {quiz} q ON q.id = cm.instance
                where cm.course = 1 and cm.id = $initial_id");
        }

        $sql = " SELECT DISTINCT o.id, o.organization_id, o.name
            FROM {organization} o ";
        $whereCondition = " WHERE o.is_edu_unit = 1 and o.status = 1 ";

        if (!empty($initial_item_id) && empty($parent_id)) {
            $whereCondition .= " AND o.id = $initial_item_id ";
        } else if (!empty($parent_id)) {
            $current_parent = $DB->get_record('organization', ['id' => $parent_id]);
            $whereCondition .= " AND o.org_parent_id = $current_parent->organization_id ";
        } else {
            $whereCondition .= " AND (o.org_parent_id = 0 OR o.org_parent_id IS NULL OR o.org_parent_id = '' ) ";
        }

        $records = $DB->get_records_sql($sql . $whereCondition);
        $items = [];
        foreach ($records as $record) {
            $has_children = $DB->record_exists('organization', ['org_parent_id' => $record->organization_id, 'is_edu_unit' => 1, 'status' => 1]);
            $items[] = [
                'id' => $record->id,
                'label' => $record->name,
                'hasChildren' => $has_children,
            ];
        }

        echo json_encode($items);
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'get_organizations') { // get_user_org_and_children_ids
    $search = optional_param('search', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $tree_initial_id = optional_param('treeInitialId', 0, PARAM_INT);
    $perpage = 10;
    $start = ($page - 1) * $perpage;

    global $USER, $DB;
    $customParams = [];
    $records = [];
    $totalcount = 0;
    $isCapabilityForAllOrg = is_capability_for_all_org(null);

    if (!empty($tree_initial_id)) {
        $sql = " SELECT DISTINCT o.id, o.organization_id, o.name
            FROM {organization} o
            JOIN {organization} o1 ON o1.status = 1 AND o1.is_edu_unit = 1 AND o.path LIKE CONCAT(o1.path, '%')
            WHERE o.is_edu_unit = 1 and o.status = 1 AND o1.id = $tree_initial_id ";
    } else if (!$isCapabilityForAllOrg) {
        $sql = "
            select distinct o1.id, o1.organization_id, o1.name
            from {role_org_assignment} roa
            JOIN {organization} o on o.id = roa.organization_id
            JOIN {organization} o1 on o1.path like concat(o.path, '%') and o1.is_edu_unit = 1 and o1.status = 1 ";

        $sql .= " where roa.user_id = $USER->id and roa.organization_id is not null and roa.organization_id != 0 ";
    } else { // System admin or all organization capability
        $sql = "SELECT DISTINCT o.id,o.organization_id, o.name FROM {organization} o WHERE o.is_edu_unit = 1 AND o.status = 1 ";
    }
    if (!empty($search)) {
        $sql .= " AND o.name LIKE :search";
        $customParams['search'] = '%' . $search . '%';
    }
    $records = $DB->get_records_sql($sql, $customParams, $start, $perpage);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'label' => $record->name
        ];
    }

    echo json_encode($items);
    exit;
}

if ($function == 'get_examination_period_category') {
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    global $DB;

    // Nếu không có từ khóa tìm kiếm, trả về danh sách mặc định
    if (empty($search)) {
        $sql = "SELECT id, name FROM {examination_period_categories} WHERE status = 1 ORDER BY name ASC";
        $count_sql = "SELECT COUNT(*) FROM {examination_period_categories} WHERE status = 1";
        $params = [];
    } else {
        $sql = "SELECT id, name FROM {examination_period_categories} WHERE status = 1 AND name LIKE :search ORDER BY name ASC";
        $count_sql = "SELECT COUNT(*) FROM {examination_period_categories} WHERE status = 1 AND name LIKE :search";
        $params = ['search' => '%' . $search . '%'];
    }

    // Đếm tổng số bản ghi
    $totalcount = $DB->count_records_sql($count_sql, $params);

    // Phân trang
    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, $params, $start, $perpage);

    // Chuẩn bị dữ liệu trả về
    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->name
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}

if ($function == 'get_examination_period') {
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    $sql = "SELECT id, name FROM {examination_period} WHERE status = 1 ";
    if (!empty($search)) {
        $sql .= " AND name LIKE :search  ";
        $params = ['search' => '%' . trim($search) . '%'];
    }

    $exam_org_ids = get_user_managing_examination_org_ancestor();
    if (!is_capability_for_all_org(null) && !empty($exam_org_ids)) {
        $sql .= ' AND organizationid IN (' . implode(',', $exam_org_ids) . ') ';
    }

    $sql .= " ORDER BY name ASC";

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, $params, $start, $perpage);
    $total_count = $DB->count_records_sql("SELECT COUNT(*) FROM ($sql) as count_temp", $params);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->name
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $total_count
    ]);
    exit;
}

if ($function == 'get_examination_period_by_orgid') {
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $org_id = optional_param('orgid', 0, PARAM_INT);
    $perpage = 10;

    $sql = "SELECT id, name FROM {examination_period} WHERE status = 1 ";
    if (!empty($search)) {
        $sql .= " AND name LIKE :search  ";
        $params = ['search' => '%' . trim($search) . '%'];
    }

    if(!empty($org_id)){
        $sql .= ' AND organizationid = :orgid ';
        $params['orgid'] = $org_id;
    }

    $exam_org_ids = get_user_managing_examination_org_ancestor();
    if (!is_capability_for_all_org(null) && !empty($exam_org_ids)) {
        $sql .= ' AND organizationid IN (' . implode(',', $exam_org_ids) . ') ';
    }

    $sql .= " ORDER BY name ASC";

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, $params, $start, $perpage);
    $total_count = $DB->count_records_sql("SELECT COUNT(*) FROM ($sql) as count_temp", $params);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->name
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $total_count
    ]);
    exit;
}

if ($function == 'get_examination_shift') {
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $self_enroll = optional_param('self_enrol', false, PARAM_BOOL);
    $perpage = 10;

    $query = "SELECT id, shiftname FROM {examination_shift} WHERE allowselfenroll = :allowselfenroll ";
    $params = ['allowselfenroll' => $self_enroll];

    if (!empty($search)) {
        $query .= " AND shiftname LIKE :search ";
        $params['search'] = '%' . $search . '%';
    }

    $query .= "ORDER BY shiftname ASC ";
    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($query, $params, $start, $perpage);
    $total_count = $DB->count_records_sql("SELECT COUNT(*) FROM ($query) as count_temp", $params);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->shiftname
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $total_count
    ]);
    exit;
}
if ($function == 'get_examination_shift_report') {
    global $DB;

    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $periodid = optional_param('periodid', 0, PARAM_INT);
    $perpage = 10;
    $params = [];

    if (empty($periodid)) {
        echo json_encode([
            'success' => true,
            'items' => [],
            'has_more' => false
        ]);
        exit;
    }

    $query = "SELECT s.id, s.shiftname 
              FROM {examination_shift} s 
              JOIN {examination_period} p ON p.id = s.examinationperiodid 
              WHERE 1=1 ";

    if(!empty($periodid)) {
        $query .= ' AND s.examinationperiodid = :periodid ';
        $params['periodid'] = $periodid;
    }
     $exam_org_ids = get_user_managing_examination_org_ancestor();
     if (!is_capability_for_all_org(null) && !empty($exam_org_ids)) {
         $query .= ' AND p.organizationid IN (' . implode(',', $exam_org_ids) . ') ';
     }

    if (!empty($search)) {
        $query .= " AND s.shiftname LIKE :search ";
        $params['search'] = '%' . $search . '%';
    }

    $query .= " ORDER BY s.shiftname ASC ";

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($query, $params, $start, $perpage);
    $total_count = $DB->count_records_sql("SELECT COUNT(*) FROM ($query) as count_temp", $params);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->shiftname
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $total_count
    ]);
    exit;
}

if ($function == 'get_quiz_category') {
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    global $DB;
    $sql = "SELECT id, name FROM {quiz_categories} WHERE name LIKE :search ORDER BY name ASC";
    $params = ['search' => '%' . $search . '%'];

    $totalcount = $DB->count_records_sql("SELECT COUNT(*) FROM {quiz_categories} WHERE name LIKE :search", $params);

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, $params, $start, $perpage);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->name
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}
if ($function == 'get_question_category') {
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    global $DB;

    $sql = "SELECT id, name 
        FROM {question_categories} 
        WHERE contextid = 1 AND status = 1 AND code IS NOT NULL";

    $params = [];

    if (!empty($search)) {
        $sql .= " AND name LIKE :search";
        $params['search'] = '%' . $search . '%';
    }

    $sql .= " ORDER BY name ASC";

//    if (empty($search)) {
//        $sql = "SELECT id, name FROM {question_categories} WHERE contextid = 1 ORDER BY name ASC";
//        $count_sql = "SELECT COUNT(*) FROM {question_categories} WHERE contextid = 1";
//        $params = [];
//    } else {
//        $sql = "SELECT id, name FROM {question_categories} WHERE contextid = 1 AND name LIKE :search ORDER BY name ASC";
//        $count_sql = "SELECT COUNT(*) FROM {question_categories} WHERE contextid = 1 AND name LIKE :search";
//        $params = ['search' => '%' . $search . '%'];
//    }

    $totalcount = $DB->count_records_sql("SELECT COUNT(*) FROM ($sql) as count_temp", $params);

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, $params, $start, $perpage);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->name
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}

if ($function == 'get_certificates') {
    $name = optional_param('q', '', PARAM_RAW);
    $search = trim($name);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    $sql = "SELECT id, name FROM {certificate} WHERE name LIKE :search ORDER BY name ASC";
    $params = ['search' => '%' . $search . '%'];
    $totalcount = $DB->count_records_sql("SELECT COUNT(*) FROM {certificate} WHERE name LIKE :search", $params);

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, $params, $start, $perpage);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'name' => $record->name
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}


if ($function == 'get_qtype') {
    $question_service = new \local_examination\service\question_service();

    $questionTypes = $question_service->get_question_types();

    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    $filteredQuestionTypes = array_filter($questionTypes, function($type) use ($search) {
        return empty($search) || stripos($type['qtype'], $search) !== false;
    });

    $totalcount = count($filteredQuestionTypes);
    $start = ($page - 1) * $perpage;
    $pagedQuestionTypes = array_slice($filteredQuestionTypes, $start, $perpage);

    $items = [];
    foreach ($pagedQuestionTypes as $type) {
        $items[] = [
            'id' => $type['qtype'],
            'name' => $type['qtype'],
            'selected' => $type['selected']
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}

if ($function == 'get_difficultylevel') {
    $question_service = new \local_examination\service\question_service();
    $difficultLevels = $question_service->getDifficultLevel();

    // Lấy tham số tìm kiếm và phân trang
    $search = optional_param('q', '', PARAM_RAW);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    // Lọc danh sách dựa trên từ khóa tìm kiếm
    $filteredDifficultLevels = array_filter($difficultLevels, function($level) use ($search) {
        return empty($search) || stripos($level['name'], $search) !== false;
    });

    // Phân trang danh sách
    $totalcount = count($filteredDifficultLevels);
    $start = ($page - 1) * $perpage;
    $pagedDifficultLevels = array_slice($filteredDifficultLevels, $start, $perpage);

    // Chuẩn bị dữ liệu trả về
    $items = [];
    foreach ($pagedDifficultLevels as $level) {
        $items[] = [
            'id' => $level['id'],
            'name' => $level['name'],
            'selected' => $level['selected']
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}

if ($function == 'seach_question_bank_examination') {
    $response = array();
    $random = optional_param('random', 0, PARAM_INT);
    $params = [
        'organization_id' => optional_param('organization', 0, PARAM_INT),
        'question_category_id' => optional_param('category', 0, PARAM_INT),
        'difficulty_level' => optional_param('difficulty_level', 0, PARAM_INT),
        'e_type' => optional_param('etype', '', PARAM_RAW),
        'page' => optional_param('page', 1, PARAM_INT),
        'per_page' => optional_param('per_page', PHP_INT_MAX, PARAM_INT),
        'question_point' => optional_param('question_point', 0, PARAM_FLOAT),
        'random' => $random
    ];
    try {
        $question_service = new \local_examination\service\question_service();
        $search = $question_service->search_examination_question($params);
        $search_results = $search['data'];
        $search_totals = $search['total'];
        if (empty($search_results)){
            $html = '<div class="question-results my-2 ms-3">';
            $html .= '<div>' . get_string('not_found', 'local_examination') . '</div>';
            $html .= '</div>';
        }
        else  {
            $html = '<div class="question-results">';
            if ($random == 1) {
                $html .= '<span>' . get_string('total_available_questions', 'local_examination', $search_totals) . '</span>';
            }else{
                $html .= '<span class="text-result">' . get_string('shift_result', 'local_examination', $search_totals) . '</span>';
            }
            $html .= '<table class="table border custom-table examination-custom-table table-question-bank" id="question_list_examination">';
            $html .= '<thead>';
            $html .= '<tr>';
            if ($random == 0) {
                $html .= '<th class="check-box-cell-init"><input type="checkbox" name="check_all" class="checkbox check-box-question-bank checkbox-align-bottom check-box-init" id="select-all"></th>';
            }
            $html .= '<th class="question-bank-td-th-init">' . get_string('question_content', 'local_examination') . '</th>';
            $html .= '<th class="question-bank-td-th-init">' . get_string('code', 'local_examination') . '</th>';
            $html .= '<th class="question-bank-td-th-init w-auto">' . get_string('qtype', 'local_examination') . '</th>';
            $html .= '</tr>';
            $html .= '</thead>';
            $html .= '<tbody id="question-list" class="examination-table-body">';

            foreach ($search_results as $question) {
                $questiontext = $question_service->trim_audio_image($question->questiontext);

                $html .= '<tr>';
                if ($random == 0) {
                    $html .= '<td class="check-box-cell-init"><input type="checkbox" class="form-check-input check-box-question-bank  question-select question-checkbox" data-is-check-all id="check_all_checkbox" data-id="'.$question->id.'"  name="q' . $question->id . '" value="1"></td>';
                }
                $html .= '<td class="question-bank-td-th-init"><div class="examination-truncate-text">' . $questiontext . '</div></td>';
                $html .= '<td class="question-bank-td-th-init">' . $question->code . '</td>';
                $html .= '<td class="question-bank-td-th-init">' . $question_service->map_etype_to_display_type($question->etype) . '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
            $html .= '</div>';
            $html .= '
                <script>
                    document.getElementById("select-all").addEventListener("change", function() {
                        var checkStatus = this.checked;
                        var checkboxes = document.querySelectorAll(".question-checkbox");
                        checkboxes.forEach(function(cb) {
                            cb.checked = checkStatus;
                        });
                    });
                    document.querySelectorAll(".question-checkbox").forEach(function(cb) {
                        cb.addEventListener("change", function() {
                            var checkboxes = document.querySelectorAll(".question-checkbox");
                            var selectAll = document.getElementById("select-all");
                            if (Array.from(checkboxes).every(function(item) { return item.checked; })) {
                                selectAll.checked = true;
                            } else {
                                selectAll.checked = false;
                            }
                        });
                    });
                </script>';
        }

        $response = [
            'params' => $params,
            'success' => true,
            'data' => $search_results,
            'difficulty_level' => optional_param('difficulty_level', 0, PARAM_INT),
            'html' => $html,
            'count' => count($search_results)
        ];
    } catch (Exception $e) {
        $response = [
            'success' => true,
            'data' => [],
            'message' => $e->getMessage(),
            'count' => 0
        ];
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
if ($function == 'count_question_random') {
    $response = array();
    $random = optional_param('random', 0, PARAM_INT);
    $params = [
        'organization_id' => optional_param('organization', 0, PARAM_INT),
        'question_category_id' => optional_param('category', 0, PARAM_INT),
        'difficulty_level' => optional_param('difficulty_level', 0, PARAM_INT),
        'e_type' => optional_param('etype', '', PARAM_RAW),
        'page' => optional_param('page', 1, PARAM_INT),
        'per_page' => optional_param('per_page', PHP_INT_MAX, PARAM_INT),
        'question_point' => optional_param('question_point', 0, PARAM_FLOAT),
        'random' => $random
    ];
    try {
        $question_service = new \local_examination\service\question_service();
        $search = $question_service->search_examination_question($params);
        $search_results = $search['data'];
        $search_totals = $search['total'];

        $response = [
            'success' => true,
            'count' => count($search_results)
        ];
    } catch (Exception $e) {
        $response = [
            'success' => true,
            'data' => [],
            'message' => $e,
            'count' => 0
        ];
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}
if ($function == 'get_list_quiz_examination_shift') {
    $examination_shift_service = new \local_examination\service\examination_shift_service();
    $query = optional_param('name', '', PARAM_TEXT);
    $page = optional_param('page', 1, PARAM_INT);
    $period_id = optional_param('periodid', 0, PARAM_INT);
    $data = $examination_shift_service->get_quizs($query, $period_id, $page);

    echo json_encode([
        'success' => true,
        'items' => $data['items'],
        'has_more' => $data['has_more']
    ]);
    exit;
}
if ($function == 'get_list_quiz_examination_shift_for_examination') {
    $examination_shift_service = new \local_examination\service\examination_shift_service();
    $query = optional_param('name', '', PARAM_TEXT);
    $page = optional_param('page', 1, PARAM_INT);
    $orgid = optional_param('organizationid', 0, PARAM_INT);
    $data = $examination_shift_service->get_quizs_for_examination($query, $orgid, $page);

    echo json_encode([
        'success' => true,
        'items' => $data['items'],
        'has_more' => $data['has_more']
    ]);
    exit;
}
if ($function == 'get_list_qtype_question_bank') {
    $data = get_list_qtype_question_bank();
    echo json_encode(['success' => true, 'data' => $data]);
    exit;
}

if ($function == 'add_participants') {
    global $OUTPUT, $USER, $DB;
    $shiftid = optional_param('shiftid', 0, PARAM_INT);

    $s_org = get_shift_organization_id($shiftid);
    require_user_roles([\local_examination\constants::ADMIN_ROLE, \local_examination\constants::EXAMINATION_PERIOD_ADMIN_ROLE], [$s_org]);

    $selectedUsers = optional_param_array('users', [], PARAM_INT);
    $shift_name = $DB->get_field('examination_shift', 'shiftname', ['id' => $shiftid]);
    $cmid = $DB->get_field_sql("select cm.id
        from {course_modules} cm
        join {modules} m on m.id = cm.module and m.name = 'quiz'
        join {examination_shift} s on s.quizid = cm.instance
        where s.id = $shiftid");
    if (!empty($shiftid) && !empty($selectedUsers)) {
        $records = [];
        $allowOrgIds = get_children_org_ids(get_shift_organization_id($shiftid), false);
        foreach ($selectedUsers as $userid) {
            $record = new stdClass();
            $record->shiftid = $shiftid;
            $record->userid = $userid;
            $record->timecreated = time();

            if (!can_add_user_to_shift($allowOrgIds, $userid)) {
                continue;
            }

            $id = $DB->insert_record('examination_participant', $record);
            $DB->set_field('examination_participant', 'sessionid', $id, ['id' => $id]);
        }
        \core\notification::info(get_string('record_added', 'local_examination'));
        echo json_encode(['success' => true]);
        exit();
    }
    echo json_encode(['success' => false, 'message' => 'Shift ID and User IDs are required']);
    exit();
}

if ($function == 'get_list_participants') {
    $shiftid = optional_param('shiftid', 0, PARAM_INT);
    $search = optional_param('search', null, PARAM_TEXT);
    $page = optional_param('page', 1 , PARAM_INT);
    $perpage = optional_param('perpage', 10, PARAM_INT);

    if (!empty($shiftid)) {
        $examination_shift_service = new examination_shift_service();
        $data = $examination_shift_service->search_examination_shift_participants($shiftid, $search, $page - 1, $perpage);
        echo json_encode([
            'success' => true,
            'items' => array_values($data['data'] ?? []),
            'has_more' => $page * $data['pagination']['perpage'] < $data['pagination']['total'],
        ]);
        exit;
    }
    echo json_encode(['success' => false, 'message' => 'Shift ID is required']);
    exit();
}
if ($function == 'get_current_quiz_section') {

    $section_id = required_param('section_id', PARAM_INT);
    $section = $DB->get_record('quiz_sections', ['id' => $section_id]);

    $section_data = [
        'id' => $section->id,
        'heading' => $section->heading,
        'description' => $section->description,
        'totalscore' => $section->totalscore,
    ];
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'data' => $section_data]);
    exit;
}

if($function == 'search_examination_candidate'){
    $search = optional_param('keyword', '', PARAM_RAW);
    $organizationid = optional_param('organizationid', 0, PARAM_INT);
    $shiftid = optional_param('s_shiftid', 0, PARAM_INT);
    $page = optional_param('page', 1, PARAM_INT);
    $perpage = 10;

    global $DB;
    $examination_participant_repo = new \local_examination\repository\examination_participant_repository();

    $sql = "SELECT DISTINCT u.id , CONCAT(u.lastname, ' ', u.firstname) AS fullname, u.employee_code as employeecode, o.name as organizationname
        FROM {user} u
        LEFT JOIN {organization} o ON o.organization_id = u.organization_id
        LEFT JOIN {role_org_assignment} roa ON roa.user_id = u.id
    ";
    $whereStatement = " WHERE 1 = 1 AND u.suspended = 0 ";

    if (!empty($search)) {
        $search = trim($search);
        $whereStatement .= " AND ( CONCAT(u.lastname, ' ',u.firstname) LIKE '%$search%' OR u.employee_code like '%$search%')";
    }

    if (!empty($organizationid)) {
        $orgid = get_organization_id_from_id($organizationid);
        $whereStatement .= " AND (u.organization_id = $orgid or exists (SELECT 1 FROM mdl_role_org_assignment roa WHERE roa.user_id = u.id AND roa.organization_id = $organizationid) )";
    }

    if (!empty($shiftid)) {
        $current_user_ids = $examination_participant_repo->get_shift_user_ids($shiftid);

        if (!empty($current_user_ids)) {
            $whereStatement .= " AND u.id NOT IN (" . implode(',', $current_user_ids) . ") ";
        }

        $root_shift_organization_id = $DB->get_field_sql("select p.organizationid
            from mdl_examination_shift s
            join mdl_examination_period p on p.id = s.examinationperiodid
            where s.id = $shiftid");
         if (!empty($root_shift_organization_id)) {
            $whereStatement .= " AND ("
                                    . "u.organization_id IN (" . implode(',', get_children_organization_ids(get_organization_id_from_id($root_shift_organization_id), false)) . ") "
                                    . "or exists (SELECT 1 FROM mdl_role_org_assignment roa WHERE roa.user_id = u.id AND roa.organization_id IN (" . implode(',', get_children_org_ids($root_shift_organization_id, false)) . ") )"
                                . ")";
        }
    }

//    if (!is_capability_for_all_org(null)) {
//        $organization_ids = implode(',', get_user_managing_examination_org_ancestor());
//        $whereStatement .= " AND org.id in ($organization_ids) ";
//    }

    $sql .= $whereStatement;
    $sql .= "ORDER BY u.firstname ASC";

    $totalcount = $DB->count_records_sql("SELECT COUNT(*) FROM ($sql) as count_temp");

    $start = ($page - 1) * $perpage;
    $records = $DB->get_records_sql($sql, [], $start, $perpage);

    $items = [];
    foreach ($records as $record) {
        $items[] = [
            'id' => $record->id,
            'code' => $record->employeecode ?? '',
            'organization' => $record->organizationname ?? '',
            'name' => $record->fullname,
        ];
    }

    echo json_encode([
        'success' => true,
        'items' => $items,
        'has_more' => ($start + $perpage) < $totalcount
    ]);
    exit;
}

if ($function === 'get_attempt_quiz_feedback') {
    try {
        $attemptid = optional_param('attempt_id', 0, PARAM_INT);

        $record = $DB->get_record('quiz_attempts', ['id' => $attemptid]);
        if ($record) {
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'title' => $record->feedbacktitle,
                    'content' => $record->feedback,
                ]
            ]);
        } else {
            echo json_encode([
                'status' => 'success',
                'data' => null
            ]);
        }
        exit;
    } catch (\Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
        ]);
        exit;
    }
}

if ($function === 'get_attempt_question_feedback') {
    try {
        $questionAttemptId = optional_param('questionAttemptId', 0, PARAM_INT);
        $subQuestionIndex = optional_param('subquestionOrder', 1, PARAM_INT);
        $record = $DB->get_record('question_attempts', ['id' => $questionAttemptId]);
        if ($record) {
            if (empty($subQuestionIndex)) {
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'title' => $record->feedbacktitle,
                        'content' => $record->feedback,
                    ]
                ]);
            } else {
                $titleArray = json_decode($record->feedbacktitle, true);
                $contentArray = json_decode($record->feedback, true);
                $subQuestionIndex = $subQuestionIndex - 1;
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'title' => $titleArray[$subQuestionIndex] ?? '',
                        'content' => $contentArray[$subQuestionIndex] ?? '',
                    ]
                ]);
            }
        } else {
            echo json_encode([
                'status' => 'success',
                'data' => null
            ]);
        }
        exit;
    } catch (\Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
        ]);
        exit;
    }
}

if ($function == 'save_attempt_quiz_feedback') {
    try {
        $title = required_param('title', PARAM_TEXT);
        $content = required_param('content', PARAM_TEXT);
        $attempt = required_param('id_hidden', PARAM_INT);

        global $DB, $USER;
        $DB->update_record('quiz_attempts', [
            'id' => $attempt,
            'feedbacktitle' => $title,
            'feedback' => $content,
            'feedbacktime' => time(),
        ]);

        // Send email
        $task = new send_notification_examination_feedback();
        $customdata = (object)[
            'userid' => $USER->id,
            'attemptid' => $attempt,
            'feedbacktitle' => $title,
            'feedback' => $content,
        ];
        $task->set_custom_data($customdata);
        $task->set_next_run_time(time());
        \core\task\manager::queue_adhoc_task($task);

        echo json_encode([
            'status' => 'success',
            'message' => 'Feedback received successfully',
            'data' => json_encode(['title' => $title, 'content' => $content,]),
            'attemptid' => $attempt,
        ]);
        exit;
    } catch (\Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
        ]);
        exit;
    }
}

if ($function == 'save_attempt_question_feedback') {
    try {
        $title = required_param('title', PARAM_TEXT);
        $content = required_param('content', PARAM_TEXT);
        $questionAttemptId = required_param('id_hidden', PARAM_INT);
        $subQuestionIndex = optional_param('subquestionOrder', 1, PARAM_INT);

        global $DB, $USER;
        if (empty($subQuestionIndex)) {
            $DB->update_record('question_attempts', [
                'id' => $questionAttemptId,
                'feedbacktitle' => $title,
                'feedback' => $content,
                'feedbacktime' => time(),
            ]);
        } else {
            $attemp = $DB->get_record('question_attempts', ['id' => $questionAttemptId]);
            $feedbackTitle = json_decode(empty($attemp->feedbacktitle) ? '{}' : $attemp->feedbacktitle);
            $feedback = json_decode(empty($attemp->feedback) ? '{}' : $attemp->feedback);
            $sqindex = $subQuestionIndex - 1;
            $feedbackTitle->$sqindex = $title;
            $feedback->$sqindex = $content;
            $DB->update_record('question_attempts', [
                'id' => $questionAttemptId,
                'feedbacktitle' => json_encode((object)$feedbackTitle),
                'feedback' => json_encode((object)$feedback),
                'feedbacktime' => time(),
            ]);
        }

        // Send email
        $task = new send_notification_examination_feedback();
        $customdata = (object)[
            'userid' => $USER->id,
            'questionattemptid' => $questionAttemptId,
            'feedbacktitle' => $title,
            'feedback' => $content,
        ];
        $task->set_custom_data($customdata);
        $task->set_next_run_time(time());
        \core\task\manager::queue_adhoc_task($task);

        echo json_encode([
            'status' => 'success',
            'message' => 'Feedback received successfully',
            'data' => json_encode(['title' => $title, 'content' => $content,]),
            'questionAttemptId' => $questionAttemptId,
        ]);
        exit;
    } catch (\Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage(),
        ]);
        exit;
    }
}
if ($function == 'get_list_participants') {
    $shiftid = optional_param('shiftid', 0, PARAM_INT);
    $search = optional_param('search', null, PARAM_TEXT);
    $page = optional_param('page', 1 , PARAM_INT);
    $perpage = optional_param('perpage', 10, PARAM_INT);

    if (!empty($shiftid)) {
        $examination_shift_service = new examination_shift_service();
        $data = $examination_shift_service->search_examination_shift_participants($shiftid, $search, $page - 1, $perpage);
        echo json_encode([
            'success' => true,
            'items' => array_values($data['data'] ?? []),
            'has_more' => $page * $data['pagination']['perpage'] < $data['pagination']['total'],
        ]);
        exit;
    }
    echo json_encode(['success' => false, 'message' => 'Shift ID is required']);
    exit();
}
if ($function == 'save_or_update_review_score') {

    $section_id = required_param('user_id', PARAM_INT);
    $section_id = required_param('shift_id', PARAM_INT);
    $section_id = required_param('score', PARAM_INT);
    $section_id = required_param('review_score', PARAM_INT);

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'data' => true]);
    exit;
}

if ($function == 'report_violation') {
    $attemptid = required_param('attemptid', PARAM_INT);

    global $DB;
    $DB->execute("UPDATE {quiz_attempts} SET violationcount = violationcount + 1 WHERE id = ?", [$attemptid]);
    echo json_encode(['success' => true, 'message' => get_string('violation_reported', 'local_examination')]);
    exit;
}

if($function == 'change_subquestion_flag'){
    $questionAttemptId = required_param('qa_id', PARAM_INT);
    $subQuestionIndex = required_param('index', PARAM_INT);
    $status = required_param('status', PARAM_INT);

    $attemp = $DB->get_record('question_attempts', ['id' => $questionAttemptId]);
    $flags = json_decode(empty($attemp->subquestionflagged) ? '{}' : $attemp->subquestionflagged);
    $flags->$subQuestionIndex = $status;
    $DB->update_record('question_attempts', [
        'id' => $questionAttemptId,
        'subquestionflagged' => json_encode((object)$flags),
    ]);

    echo json_encode(['success' => false, 'message' => 'oke']);
    exit();
}

if ($function == 'check_shift_password') {
    $shiftid = required_param('shiftid', PARAM_INT);
    $password = required_param('password', PARAM_TEXT);
    $can_attempt = check_shift_password($shiftid, $password);
    if ($can_attempt) {
        echo json_encode(['success' => true, 'message' => 'Password is correct']);
        exit;
    } else {
        $remaining_time = get_shift_password_attempt_remaining_time($shiftid);
        if (!empty($remaining_time)) {
            echo json_encode(['success' => false, 'message' => get_string('retry_after', 'local_examination', $remaining_time)]); exit;
        }
        echo json_encode(['success' => false, 'message' => get_string('password_error', 'local_examination')]);
        exit;
    }
}

if ($function == 'self_examination_register') {
    global $USER, $DB;
    $shiftid = required_param('shiftid', PARAM_INT);

    if (!can_self_enrol_shift($shiftid)) {
        echo json_encode(['success' => false, 'message' => 'You dont have permission.']);
        exit;
    }

    $exam_enrol_service = new \local_examination\service\examination_participant_service();
    $data_obj = (object) [
        'shiftid' => $shiftid,
        'userid' => $USER->id
    ];
    $exam_enrol_service->save_or_update_examination_participant($data_obj);
    echo json_encode(['success' => true, 'message' => '']);
    exit;
}

if ($function == 'move_slot') { // Need to add transaction and add separator if is only slot in section
    $slotid = required_param('id', PARAM_INT);
    $quizid = required_param('quizid', PARAM_INT);
    $section = required_param('sectionId', PARAM_INT);
    $page = optional_param('page', 0, PARAM_INT);
    $previousid = optional_param('previousid', 0, PARAM_INT);
    $cmid = get_cmid_by_quizid($quizid);

    global $DB;
    $trans = $DB->start_delegated_transaction();

    if (is_last_slot_in_section($slotid)) {
        $slot = $DB->get_record('quiz_slots', ['quizid' => $quizid, 'id' => $slotid]);
        add_part_separator_to_page($quizid, $slot->page, $cmid);
    }

    if (empty($page)) {
        $page = $DB->get_field_sql("select qsl.page
            from mdl_quiz_sections qs
            join mdl_quiz_slots qsl on qsl.quizid = qs.quizid and qsl.slot = qs.firstslot
            where qs.id = $section");
    }

    $slot = $DB->get_record('quiz_slots', ['quizid' => $quizid, 'id' => $slotid]);

    if (empty($previousid)) {
        $previousid = get_examination_previous_id($quizid, $page, $cmid, $slot->slot);
    }

    $slot = $DB->get_record('quiz_slots', ['quizid' => $quizid, 'id' => $slotid]);
    $previous_slot = $DB->get_record('quiz_slots', ['quizid' => $quizid, 'id' => $previousid]);

    if (empty($previous_slot) || $previous_slot->id == $slotid) {
        echo json_encode(['success' => true, 'message' => 'Done']);
        exit;
    }

    // add separator if is only slot in section

    // update slot index
    if ($previous_slot->slot > $slot->slot) { // down
        reindex_slot($quizid, $previous_slot->slot + 1, 0, +1);
        $DB->execute("UPDATE {quiz_slots} SET slot = ?, page = ? WHERE quizid = ? AND id = ?", [$previous_slot->slot + 1, $page, $quizid, $slot->id]);
        reindex_slot($quizid, $slot->slot + 1, 0, -1);
    } else if ($previous_slot->slot < $slot->slot) { // up
        reindex_slot($quizid, 0, $previous_slot->slot, -1);
        $DB->execute("UPDATE {quiz_slots} SET slot = ?, page = ? WHERE quizid = ? AND id = ?", [$previous_slot->slot, $page, $quizid, $slot->id]);
        reindex_slot($quizid, 0, $slot->slot, +1);
    }

    // update section firstslot
    if ($previous_slot->slot > $slot->slot) { // down
        reindex_section($quizid, $slot->slot + 1, $previous_slot->slot, -1);
    } else if ($previous_slot->slot < $slot->slot) { // up
        reindex_section($quizid, $previous_slot->slot + 1, $slot->slot, +1);
    }

    // If any pages are now empty, remove them.
    $emptypages = $DB->get_fieldset_sql("
                SELECT DISTINCT page - 1
                  FROM {quiz_slots} slot
                 WHERE quizid = ?
                   AND page > 1
                   AND NOT EXISTS (SELECT 1 FROM {quiz_slots} WHERE quizid = ? AND page = slot.page - 1)
              ORDER BY page - 1 DESC
                ", [$quizid, $quizid]);

    foreach ($emptypages as $emptypage) {
        $DB->execute("
                    UPDATE {quiz_slots}
                       SET page = page - 1
                     WHERE quizid = ?
                       AND page > ?
                    ", [$quizid, $emptypage]);
    }

    $trans->allow_commit();
    echo json_encode(['success' => true, 'message' => 'Done']);
    exit;
}
if ($function == 'get_all_subjects') {
    try {
        global $DB;
        $sql = "SELECT DISTINCT es.* FROM mdl_examination_subjects es WHERE es.status = 1";
        $records = $DB->get_records_sql($sql, []);
        echo json_encode(array_values($records));
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function == 'update_quiz_page') {
    try {
        $slotid = required_param('slotid', PARAM_INT);
        $quizid = required_param('quizid', PARAM_INT);
        $value = required_param('value', PARAM_INT); // 1: Merge page, 2: Split page

        $quizobj = \mod_quiz\quiz_settings::create($quizid);
        $quiz = $quizobj->get_quiz();
        $structure = $quizobj->get_structure();
        $slots = $structure->update_page_break($slotid, $value);
        quiz_delete_previews($quiz);
        echo json_encode(array('success' => true));
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        exit;
    }
}

if ($function = 'get_max_bytes_info') {
    try {
        $size = get_config('moodle', 'maxbytes');

        if ($size == 0) {
            // Will get the minimum of upload_max_filesize or post_max_size.
            $size = get_max_upload_file_size();
        }

        $display_size = display_size($size,0);

        echo json_encode(array('success' => true, 'maxbytes' => $size, 'displaysize' => $display_size));
        exit;
    } catch (\Exception $e) {
        echo json_encode(array('success' => true, 'maxbytes' => 5 * 1024 * 1024, 'displaysize' => '5MB'));
        exit;
    }
}

echo json_encode(['success' => false, 'message' => 'Invalid function']);
exit;