.course_edit_module,
.course_edit,
.course_edit_completion {
    #region-main-box {
        #block-region-side-bottom {
            display: none;
        }
    }
    /* #id_saveandreturn {
        display: none!important;
    } */

    #region-fullwidthtop-blocks-indicator,
    #region-top-blocks-indicator,
    #region-bottom-blocks-indicator,
    #region-fullwidthbottom-blocks-indicator,
    #card-editing-container,
    .block-controls.header {
        display: none;
    }

    .editmode-switch-form {
        display: none !important;
    }

    .secondary-navigation.edw-tabs-navigation {
        display: none;
    }

    #page-header {
        display: none !important;
    }

    .drawer-right,
    .drawer-right-toggle {
        display: none;
    }

    .drawer.drawer-left {
        visibility: visible !important;
    }

    .drawer-custom.drawer-left {
        position: absolute;
        left: 0 !important;

        .drawerheader {
            display: none !important;
        }
    }

    #add-block-float-menu {
        display: none;
    }

    .drawer-left-toggle {
        visibility: hidden !important;
        z-index: 999 !important;
    }

    @media (max-width: 991.98px) {
        .course-content {
            margin-left: 0 !important;
        }
        .drawer-custom.drawer-left {
            .drawerheader {
                display: block !important;
            }
        }

        .drawer-left-toggle {
            display: block !important;
            left: 65px !important;
            visibility: visible !important;
        }

        .drawer.drawer-left {
            transition: left 0.2s ease, top 0.2s ease, bottom 0.2s ease, visibility 0.2s ease;
            left: calc(-480px + -10px);
            visibility: hidden !important;
            padding: 0 !important;
            position: fixed !important;
            left: 80px !important;
            top: 100px !important;

            nav#courseindex {
                background-color: white !important;
            }

            &.show {
                visibility: visible !important;
            }
        }
    }

    @media (max-width: 767.98px) {
        .drawer-left-toggle {
            left: 0 !important;
        }
    }

    .fitem_id_highlights {
        .fitemtitle {
            display: none;
        }
    }

    #course-highlights-container {
        .add-highlight-btn {
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            vertical-align: middle;
        }
        .add-highlight-btn:disabled {
            color: #707070!important;
            background-color: #E0E0E0!important;
            border: 1px solid #E0E0E0!important;
            &:hover {
                background-color: #E0E0E0!important;
                border: 1px solid #E0E0E0!important;
                color: #707070!important;
            }
        }
        .add-highlight-btn:visited,
        .add-highlight-btn:active,
        .add-highlight-btn:focus {
            color: #FFFFFF;
            background-color: #EE0033;
            border: 1px solid #EE0033;
            outline: none;
            box-shadow: none;
            svg {
                path {
                    stroke: #FFF;
                }
            }
        }
        .add-highlight-btn:hover {


            svg {
                path {
                    stroke: #FFF;
                }
            }
        }

        .highlights-note {
            font-weight: 400;
            font-style: italic;
            font-size: 14px;
            line-height: 22px;
            color: #44494D;
            margin-top: 16px;
        }

        .highlights-list {
            margin-top: 24px;
            .input-group {
                flex-wrap: unset;
                gap: 24px;
                input {
                    border-radius: 8px;
                }

                .input-group-append {
                    button {
                        border-radius: 8px;
                        height: 48px;
                        width: 48px;
                        border: 1px solid #E0E0E0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        &:hover {
                            border-color: #EE0033;
                            background-color: #EE0033;
                            svg {
                                path {
                                    stroke: #FFF;
                                }
                            }
                        }
                    }
                }
            }
        }

        /* Highlight input error styling */
        .highlight-input.is-invalid {
            border-color: #EE0033;
        }

        .highlight-error {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            color: #dc3545 !important;
        }

        .highlight-item {
            margin-bottom: 1rem;
        }

        .highlight-item .input-group {
            margin-bottom: 0;
        }
    }

}

body.dark-mode {
    .remove-highlight-btn {
        border-color: #333333;
        svg {
            path {
                stroke: #333333;
            }
        }
    }
    &#page-local-workplace-course_edit .fieldset-header {
        background: #060606 !important;
    }

    .catergory_container {
        border-color: #333333!important;
    }
    #selected-categories-container {
        border-color: #333333!important;
    }
}

.course_edit_module {
    .block.block_edwiserratingreview {
        display: block;
    }
}

#page-local-workplace-course_edit_module {
    #theme_remui-drawers-courseindex.drawer {
        position: relative;
    }
}

#changenumsections,
.divider.bulk-hidden:not(.always-visible) {
    display: none !important;
}

#page #topofscroll>div.container {
    position: relative;
}

@media (min-width: 1650px) {
    #page #topofscroll>div.container {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

#page-course-view-remuiformat .btn.add-content.section-modchooser {
    width: 100%;
    background-color: #FDE6EB !important;
    color: #FF0033 !important;
    font-weight: normal !important;
    border: 2px dashed #FF859F !important;
    border-radius: 8px !important;
}

body#page-course-view-remuiformat .activity .activity-item:not(.activityinline).focus-control {
    padding: 8px;
    border-color: #E0E0E0;

    .activity-actions {
        align-self: center !important;
    }
}

body#page-course-view-remuiformat #page .activityiconcontainer {
    width: 38px;
    height: 38px;
    align-self: center !important;
}

body.course_edit_module {
    &.sidebar-collapsed #page.drawers {
        margin-left: 64px !important;
    }
    #region-top-blocks.has-blocks {
        margin: 0 !important;
    }
    #theme_remui-drawers-courseindex .drawercontent #courseindex #courseindex-content .courseindex .courseindex-section .courseindex-item-content .courseindex-sectioncontent .courseindex-item .edw-section-content-wrapper .courseindex-link, #theme_remui-drawers-courseindex .drawercontent #courseindex #courseindex-content .courseindex .courseindex-section .courseindex-item-content .courseindex-sectioncontent .courseindex-item .edw-section-content-wrapper .courseindex-name {
        margin-left: 0;
    }
    #theme_remui-drawers-courseindex .drawercontent #courseindex #courseindex-content .courseindex .courseindex-section .courseindex-section-title {
        padding: 12px 16px!important;
    }

    .courseindex-link {
        pointer-events: none;
    }

    #theme_remui-drawers-courseindex {
        padding-left: 43px;
        padding-right: 32px;
        top: 108px;
        background: none;
        position: absolute;

        nav.courseindex {
            border: 1px solid #E0E0E0;
            border-radius: 12px;

            .p-mb-4 {
                margin-bottom: 16px;
                padding: 12px;
                border-bottom: 1.75px solid #E0E0E0;

                .drawercontent-divier {
                    display: none;
                }
            }
        }

        .courseindex-scrollable {
            border-radius: unset;
        }
        #courseindex-content {
            border: none !important;
            .courseindex-section {

            }
        }


        .drawercontent {
            padding: 0 !important;
        }
    }

    .activityiconcontainer {
        &.smaller {
            width: 48px;
            height: 48px;
            max-width: 48px;
            max-height: 48px;
            background-color: #FDE6EB;
            border-radius: 8px;

            .icon {
                width: 24px;
                height: 24px;
                max-width: 24px;
                max-height: 24px;
            }
        }

        &.administration:not(.isbranded),
        &.assessment:not(.isbranded),
        &.collaboration:not(.isbranded),
        &.communication:not(.isbranded),
        &.content:not(.isbranded),
        &.interactivecontent:not(.isbranded) {
            background: #FDE6EB !important;
        }
    }

    .modchoosercontainer .optionscontainer .option .optionname {
        font-size: 15px;
        color: #120C0D;
    }

    .remui-format-list, .remui-format-card {
        .sections {
            .section {
                .content {
                    h3 a {
                        color: #120C0D !important;
                    }

                    & > .course-section-summary-wrapper {
                        padding-right: 8px !important;
                    }
                }
            }

            .wdm-add-new-section {
                border: none !important;
            }

            > .course-section-summary-wrapper, .section {
                border: none !important;

                .course-section-header {
                    gap: 8px;

                    .name-progress-wrapper {
                        width: 89% !important;

                        &:hover {
                            .quickeditlink.aalink {
                                display: none !important;
                            }

                            a {
                                border-radius: 8px;
                                text-decoration: none;
                                box-shadow: 0 0 0 1.75px #FF859F !important;
                                border: none !important;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                width: 100% !important;
                                display: block;
                            }
                        }
                    }
                }

                .action-menu {
                    .dropdown-menu {
                        border: 1px solid #E0E0E0;
                        box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.12);
                    }

                    .dropdown-item {
                        display: flex;
                        align-items: flex-end;
                        justify-content: start;
                        gap: 8px;

                        &.menu-action {
                            &.view,
                            &.editing_highlight,
                            &.duplicate,
                            &.editing_showhide,
                            &.move.waitstate,
                            &.moveup.whilenostate,
                            &.movedown.whilenostate,
                            &.editing_movecm.cm-edit-action,
                            &.editing_moveright.cm-edit-action,
                            &.editing_moveleft.cm-edit-action,
                            &.editing_duplicate.cm-edit-action,
                            &.editing_assign.cm-edit-action,
                            &[data-action="cmHide"] {
                                display: none !important;
                            }

                            &.editing_delete.text-danger .menu-action-text {
                                color: #FF0033 !important;
                            }
                        }

                        .menu-action-text {
                            font-size: 14px;
                            color: #120C0D;
                            line-height: 1.2;
                        }
                    }

                    .dropdown-subpanel.dropleft {
                        display: none !important;
                    }

                    .dropdown.show>.dropdown-toggle {
                        background-color: #e9ecef !important;
                    }
                }
    
                &:not(#section-0) {
                    border: none;
                    padding: 0 !important;
                    margin-top: 0 !important;
                }

                .sectionbadges {
                    display: none !important;
                }
            }
        }

        .activity-item .activity-grid .activity-dates {
            display: none;
        }

        .activity {
            margin-top: 20px !important;

            .activity-item {
                .activity-completion,
                .activity-altcontent,
                .activity-groupmode-info {
                    display: none !important;
                }

                &:hover .activityiconcontainer {
                    mix-blend-mode: normal !important;
                }

                .activity-instance.activity-name-area {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .description .activity-dates,
            .activityinstance > .contentafterlink,
            .activity-information,
            .quickeditlink.aalink {
                display: none !important;
            }

            .instancename {
                color: #120C0D;
                font-weight: 500;
            }
        }
    }

    .section-summary-activities {
        display: none;
    }

    .divider.bulk-hidden {
        margin-top: 48px !important;

        .divider-content {
            width: 100%;
            padding: 0 !important;
        }
    }

    .section-add-content {
        margin-top: 84px !important;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        hr {
            width: 100%;
            margin: .5rem .25rem;
            border-top: 2px dashed #E0E0E0;
        }

        .wdm-add-new-section {
            position: absolute;
        }
    }

    .noactivitiesinsection {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 40px;

        p {
            margin-top: 18px;
        }
    }

    input.ignoredirty.form-control {
        font-size: 18px !important;
        color: #120C0D;
        font-weight: 600;
        height: 32px;
        padding: 0 8px !important;
    }

    .sectionname.card-title input.ignoredirty.form-control:focus {
        box-shadow: 0 0 0 1.75px #FF859F !important;
        border: none !important;
    }

    .activityname input.ignoredirty.form-control:focus {
        box-shadow: none !important;
        border: 2px dashed #FF859F !important;
    }
}

h3.sectionname {
    .quickeditlink.aalink {
        display: none;
    }
    .inplaceeditable.inplaceeditingon .editinstructions {
        margin-top: -28px;
        background-color: #FDE6EB;
        font-size: 12px;
        color: #120C0D;
        border: none;
        padding: 6px;
    }

    .inplaceeditable.inplaceeditable-text {
        display: inherit;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        height: 36px;
        padding-top: 2px;
        padding-left: 2px;
        padding-right: 2px;

        a {
            padding-left: 8px;
            padding-right: 8px;
            font-size: 20px !important;
            width: 100% !important;
        }
    }

    &:hover {
        background-color: transparent !important;
    }
}

body#page-course-view-remuiformat .remui-format-list.all-section-format ul.sections li.section .content ul.section .activity .activitytitle {
    width: -webkit-fill-available;

    .media-body {
        overflow: hidden;
    }
}

body#page-course-view-remuiformat .activity .activitytitle .media-body .activityname.color-font-headings {
    display: flex !important;
    align-items: center;
}

.activityname {
    width: -webkit-fill-available;
    height: 40px;

    > .inplaceeditable.inplaceeditingon .editinstructions {
        margin-top: -30px;
        background-color: #FDE6EB;
        font-size: 12px;
        color: #120C0D;
        border: none;
        padding: 6px;
        line-height: 1.5;
    }

    .inplaceeditable.inplaceeditable-text {
        padding: 4px;
        display: inline-block !important;
        width: -webkit-fill-available;
        text-overflow: ellipsis;
        overflow: hidden;

        &.inplaceeditingon {
            padding-right: 0;
        }

        a {
            padding-left: 8px;
            padding-right: 8px;
            font-size: 16px !important;
            width: 100% !important;
        }
    }

    .instancename {
        text-overflow: ellipsis;
        overflow: hidden;
    }

    &:hover {
        background-color: transparent !important;

        .instancename {
            text-decoration: none;
        }

        a {
            text-decoration: none;
            display: inline-flex !important;
            border-radius: 8px;
            box-shadow: none !important;
            border: 2px dashed #FF859F !important;
        }
    }
}
#page-course-view-remuiformat #page-content .section.main.card {
    margin-bottom: 0 !important;
}
.section-content {
    .section-item {
        display: grid;
        grid-template-columns: repeat(3, minmax(auto, 1fr));
        gap: 20px;

        .activity-item {
            border: 1px solid #E0E0E0;
            border-radius: 16px;
        }
    }
}

.progress-tabs {
    margin-top: 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    h2 {
        color: #120C0D;
        font-size: 24px;
        font-weight: 600;
    }

    .nav-tabs {
        border: none;
        display: flex;

        .nav-item-custom {
            position: relative;
            flex: 1;
            margin-right: 6px;

            .nav-link-custom {
                max-height: 32px;
                display: flex;
                align-items: center;
                background-color: #F0F0F0;
                color: #666666;
                text-decoration: none;
                border: none;
                position: relative;
                font-weight: 500;
                transition: all 0.3s ease;
                white-space: nowrap;

                .label {
                    font-size: 14px;
                    font-weight: 600;
                    padding: 0 30px;
                }

                &.active {
                    background-color: #FED7DF;
                    color: #333333;
                }

                &:hover {
                    text-decoration: none;
                    color: inherit;
                }
            }
        }

        .nav-item-custom:first-child {
            .nav-border-wrapper {
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    right: -1px;
                    bottom: -1px;
                    background-color: #E0E0E0;
                    border-radius: 27px 0 0 27px;
                    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%);
                    z-index: 1;
                }
            }

            .nav-link-custom.active+.nav-border-wrapper::before,
            .nav-border-wrapper:has(.nav-link-custom.active)::before {
                background-color: #FED7DF;
            }

            .nav-link-custom {
                position: relative;
                border-radius: 27px 0 0 27px;
                clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%);
                z-index: 2;

                background-color: #FFFFFF;
                margin: 0.5px;
            }

            .nav-link-custom.active {
                background-color: #FED7DF;
            }

            .nav-item-custom .nav-link-custom.active {
                background-color: #FED7DF;
                position: relative;
                z-index: 2;
            }
        }

        .nav-item-custom:not(:first-child):not(:last-child) {
            margin-left: -15px;

            .nav-border-wrapper {
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    right: -1px;
                    bottom: -1px;
                    background-color: #E0E0E0;
                    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%, 15px 50%);
                    z-index: 1;
                }
            }

            .nav-border-wrapper:has(.nav-link-custom.active)::before {
                background-color: #FED7DF;
            }

            .nav-link-custom {
                position: relative;
                background-color: #FFFFFF;
                clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%, 15px 50%);
                z-index: 2;
                border-radius: 0;

                &.active {
                    background-color: #FED7DF;

                    &::before {
                        display: none;
                    }
                }
            }
        }

        .nav-item-custom:last-child {
            margin-left: -15px;

            .nav-border-wrapper {
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    right: -1px;
                    bottom: -1px;
                    background-color: #E0E0E0;
                    border-radius: 0 28px 28px 0;
                    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 15px 50%);
                    z-index: 1;
                }
            }

            .nav-border-wrapper:has(.nav-link-custom.active)::before {
                background-color: #FED7DF;
            }

            .nav-link-custom {
                position: relative;
                background-color: #FFFFFF;
                border-radius: 0 27px 27px 0;
                clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 15px 50%);
                z-index: 2;

                &.active {
                    background-color: #FED7DF;

                    &+.nav-border-wrapper::before {
                        display: none;
                    }
                }
            }
        }
    }
}

#page-content .progress-tabs {
    @media (max-width: 1200px) {		
        display: flex;		
        flex-direction: column;		
        align-items: flex-start;		
        justify-content: flex-start;		
    }

    @media (max-width: 1023.98px) {
        h2 {
            font-size: 20px;
        }
    }

    @media (max-width: 767.98px) {
        .nav-tabs {
            background: #FFFFFF;
            margin-top: 8px;

            .nav-item-custom {
                .nav-link-custom {
                    .label {
                        padding: 0 10px;
                    }
                }
            }
        }
    }

    @media (max-width: 480px) {
        h2 {
            font-size: 18px;
        }

        .nav-tabs {
            width: 100%;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            background: #FFFFFF;
            margin-top: 8px;

            .nav-item-custom {
                margin-left: 0;
                margin-right: 6px;
                flex: 1 1;
                min-width: 0;

                .nav-link-custom {
                    .label {
                        font-size: 12px;
                        padding: 0;
                        word-break: break-word;
                        overflow-wrap: break-word;
                        white-space: normal;
                        text-align: center;
                        display: block;
                        width: 100%;
                    }
                }
            }
        }
    }
}

/* disable tab in course edit srceen */
.nav-item-custom .nav-link-custom.disabled {
    cursor: not-allowed !important;
    pointer-events: none !important;
    opacity: 0.6 !important;
    color: #999999 !important;
}

.nav-item-custom .nav-link-custom.disabled:hover {
    cursor: not-allowed !important;
}

.nav-item-custom .nav-border-wrapper:has(.nav-link-custom.disabled)::before {
    opacity: 0.6 !important;
    background-color: #CCCCCC !important;
}

@media (max-width: 480px) {
    .nav-item-custom .nav-link-custom.disabled {
        cursor: not-allowed !important;
        pointer-events: none !important;
        opacity: 0.6 !important;
        color: #999999 !important;
        background-color: #F5F5F5 !important;
        border-color: #CCCCCC !important;
    }
}

.course-content {
    margin-left: 280px;

    .section-details {
        display: none !important;
    }

    .course-header-image {
        width: 100%;
        height: 320px;
        border-radius: 24px;
        margin-top: 16px;
        object-fit: cover;
    }

    @media (max-width: 480px) {
        .course-header-image {
            height: 220px;
        }
    }
}

@media (min-width: 1024px) {
    .modchoosercontainer .optionscontainer .option {
        flex-basis: 140px;

        .optioninfo {
            min-height: 140px;
        }
    }
}
@media (max-width: 767.98px) {
    .drawer-left-toggle {
        left: 0 !important;
        top: 180px !important;
    }
    #theme_remui-drawers-courseindex {
        top: 0 !important;
        padding: 12px !important;
    }
    #region-top-blocks {
        display: none !important;
    }
    #page.drawers .main-inner {
        margin-top: 0;
    }
    /*#page #topofscroll>div.container {*/
    /*    padding-left: 0;*/
    /*    padding-right: 0;*/
    /*}*/
    .remui-format-list .sections .section {
        .side.right {
            width: auto !important;
        }

        .course-section-header {
            flex-direction: row !important;
        }
    }
}

#fitem_id_summary_editor .tox .tox-menu.tox-collection.tox-collection--list {
    top: -340px !important;
    bottom: auto !important;
    max-height: 500px !important;
}

#fitem_id_customfield_tool_courserating_editor .tox .tox-menu.tox-collection.tox-collection--list {
    top: -435px !important;
    bottom: auto !important;
    max-height: 500px !important;
}

.custom-select#id_organization {
    width: 100%;
}

#selected-categories-container {
    border: 1px solid #E0E0E0 !important;
    padding: 10px;
    min-height: 68px;
    border-radius: 8px !important;
}


.form-group.fitem_categories {
    margin-bottom: 8px;
}

.catergory_container {
    padding: 16px 0;
    border: 1px solid #E0E0E0;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.catergory_container .catergory_select {
    padding: 0 16px;
}

.catergory_container .catergory_row {
    padding: 0 4px;
}

.course_edit_module .fitem .col-form-label,
.course_edit .fitem .col-form-label,
.course_edit_completion .fitem .col-form-label {
    width: 100% !important;
    max-width: 100% !important;
    flex: auto !important;
}

.course_edit_module .fitem .form-control,
.course_edit .fitem .form-control,
.course_edit_completion .fitem .form-control {
    width: 100% !important;
    max-width: 100% !important;
    flex: auto !important;
}

.course_edit_module .fitem select.custom-select,
.course_edit .fitem select.custom-select,
.course_edit_completion .fitem select.custom-select {
    width: 100% !important;
    max-width: 100% !important;
    flex: auto !important;
}

.course_edit_module #fitem_id_password #id_password,
.course_edit #fitem_id_password #id_password,
.course_edit_completion #fitem_id_password #id_password {
    height: calc(1.5em + 1.375rem + 2px) !important;
}

.course_edit_module .fitem .custom-autocomplete,
.course_edit .fitem .custom-autocomplete,
.course_edit_completion .fitem .custom-autocomplete {
    padding: 0 !important;
}

.course_edit_module .fitem.custom-autocomplete,
.course_edit .fitem.custom-autocomplete,
.course_edit_completion .fitem.custom-autocomplete {
    padding: 0 !important;
}

.course_edit_module .fitem .edw-form-label,
.course_edit .fitem .edw-form-label,
.course_edit_completion .fitem .edw-form-label {
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    color: #120C0D !important;
}

.edw-form-label {
    font-weight: 400;
    font-style: Regular;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    color: #120C0D !important;
}
.form-group .form-control {
    font-weight: 400;
    font-size: 16px;
    color: #120C0D !important;
}

.form-group .custom-select {
    font-weight: 400;
    font-size: 16px;
    color: #120C0D !important;
}
.upload-drag-drop {
    font-weight: 600;
    gap: 16px;
    color: #120C0D;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
}

.upload-text {
    color: #FF0033;
}

/* questionnaire */
#page-mod-questionnaire-questions {
    .mod_questionnaire_questionspage {
        .nav-tabs {
            display: none;
        }
    }
}

.block_vlms_q_a {
    display: none;
}

.section-summary-editor {
    margin-top: 24px !important;
}

.modal {
    &.add-section-modal {
        .modal-dialog {
            max-width: 968px !important;
            margin: 5rem auto !important;
    
            @media (max-width: 1023.98px) {
                max-width: 640px !important;
            }
    
            @media (max-width: 767.98px) {
                max-width: 380px !important;
            }
        
            .modal-footer {
                border: none !important;
                padding-top: 0;
                
                .btn {
                    font-size: 16px !important;
                    font-weight: 500 !important;
                }
            }
    
            .tox.tox-tinymce {
                height: 248px !important;
    
                @media (max-width: 1023.98px) {
                    height: 190px !important;
                }
            }
        }

        .modal-header {
            padding: 16px 24px;
        }
    }

    .modchooser {
        max-width: 640px !important;

        .modal-dialog {
            margin: 5rem auto !important;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid #E0E0E0;
        }
        .modal-header .edw-icon-Cancel:before {
            content: "";
            display: inline-block;
            width: 18px;
            height:18px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'18\' height=\'18\' viewBox=\'0 0 18 18\' fill=\'none\'><path d=\'M14.0625 3.9375L3.9375 14.0625\' stroke=\'%236F767E\' stroke-width=\'1.5\' stroke-linecap=\'round\' stroke-linejoin=\'round\'/><path d=\'M14.0625 14.0625L3.9375 3.9375\' stroke=\'%236F767E\' stroke-width=\'1.5\' stroke-linecap=\'round\' stroke-linejoin=\'round\'/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }

        .optionscontainer {
            gap: 12px !important;
            padding: 0 !important;
        }

        .modal-body {
            height: 328px !important;
            min-height: auto;
        }
    }
}
#theme_remui-drawers-courseindex {
    @media (max-width: 1365px) and (min-width: 1195px) {
        padding-left: 24px !important;
    }

    @media (max-width: 1194px) and (min-width: 834px) {
        padding-left: 0 !important;
        padding-right: 60px !important;
        top: 162px !important;
    }
}

.form-filemanager-custom .filemanager-toolbar {
    display: none !important;
}
#page-local-workplace-course_edit .moodle-dialogue.filepicker{
    display: none !important;
}

.form-filemanager-custom .view-image-file-manager {
    display: flex !important;
    align-items: center;
    gap: 20px;
}

.form-filemanager-custom .view-image-file-manager .fp-filename {
    margin-top: 0;
    width: 100% !important;
    color: #120C0D;
    font-weight: 500;
    font-size: 14px;
}

.form-filemanager-custom .fp-content {
    display: flex;
    align-items: center;
}
.form-filemanager-custom .filemanager-container:hover{
    border-color: #007cba;
    background: #f9f9f9;
}

.form-filemanager-custom .dndupload-arrow {
    height: unset;
}

.form-filemanager-custom .dndupload-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.form-filemanager-custom .fp-iconview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1;
}

#page-local-examination-edit_examination_shift .fp-file-delete-custom {
    display: none;
}
#page-mod-examination-mod{
    #select2-id_quizid-results{
        max-height: 345px;
    }
    .select2-container--default .select2-search--dropdown .select2-search__field {
        outline-color: #dc3545;
        border-color: #dc3545;
    }
    
}