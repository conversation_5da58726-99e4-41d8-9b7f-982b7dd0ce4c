.program-info-card {
    display: grid;
    grid-template-columns: repeat(2, minmax(auto, 1fr));
    gap: 16px 32px;
    margin-bottom: 32px;
}
.program-info-new {
    margin-bottom: 16px;
}
.program-info-description {
    white-space: pre-line;
    font-size: 16px;
    color: #120C0D;
}
.small-title {
    color: #120C0D;
    font-size: 18px;
    margin-bottom: 16px;
}

.program-info-new.img {
    height: 404px;
    width: 100%;
    img {
        border-radius: 24px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }
}

.program-container .breadcrumb {
    gap: 4px;
    justify-content: flex-start;
    align-items: center;
    line-height: 1;
}

.program-container .breadcrumb .breadcrumb-item a {
    color: #6F767E !important;
    font-weight: 400;
    font-size: 14px;
}

.program-container .breadcrumb .breadcrumb-item.active {
    color: #FF0033 !important;;
    font-weight: 600;
}

.program-info-card .program-info-item label {
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    color: #6F767E;
}
.program-info-card .program-info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.program-info-card .program-info-item strong {
    font-size: 14px;
    font-weight: 500;
    color: #44494D;
}

.program-container .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 4L10 8L6 12" stroke="%236F767E" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

.program-container .sub-title {
    font-size: 20px;
    color: #120C0D;
    font-weight: 600;
    margin-bottom: 16px;
}

.programstatus {
    display: inline-block;
    font-size: 12px !important;
    padding: 2px 8px;
    width: fit-content;
    border: none;
    border-radius: 8px;
}
.bg-orange {
    background-color: #FF9500;
}
#page-local-workplace-view_program .custom-pagination-container.table-pagination {
    margin: 0;
}
.program-container .course-list-body {
    display: block;
}
.program-container {
    .custom-table tbody {
        max-height: 520px;
    }
    .dateinprogress {
        white-space: nowrap;
    }
    .text-required {
        font-size: 14px !important;
    }
}
.program-container {
    @media (max-width: 1366px) {
        .text-required {
            font-size: 12px !important;
        }
        .course-library-title {
            font-size: 24px;
        }

        .sub-title {
            font-size: 16px;
        }

        .program-info-card {
            margin-bottom: 24px;
        }
        .breadcrumb {
            margin-bottom: 16px;
        }
    }
    @media (max-width: 834px) {
        .course-library-title {
            font-size: 20px;
        }
        .program-info-card .program-info-item strong {
            font-size: 14px;
        }
    }
    @media (max-width: 428px) {
        .course-library-title {
            font-size: 16px;
            margin-bottom: 16px;
        }
        .program-info-card {
            margin-bottom: 16px;
        }
        .sub-title {
            margin-bottom: 12px;
        }
    }
}

#page-local-workplace-view_program {
    @media (max-width: 1366px) {
        .small-title {
            font-size: 16px;
        }
    }
    @media (max-width: 834px) {
        .program-info-new.img {
            margin-bottom: 24px;
            height: 420px;
        }
        .small-title {
            margin-bottom: 24px;
        }
        .sub-title {
            margin-bottom: 20px;
        }
        .course-library-title {
            margin-bottom: 24px;
        }
        .breadcrumb {
            margin-bottom: 8px;
        }
    }
    @media (max-width: 834px) {
        .program-info-new.img {
            height: 210px;
        }
        .small-title {
            margin-bottom: 16px;
        }
        .sub-title {
            margin-bottom: 12px;
        }
        .course-library-title {
            margin-bottom: 16px;
        }
    }
}
/* training_program_categories */
#page-local-workplace-training_program_categories{
    .item-name-focus .order-up,
    .item-name-focus .order-down,
    .item-name-focus .custom-dropdown-wrapper button.custom-dropdown-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
    }
    .custom_lenght_text{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80%;
    }
    .list-unstyled {
        position: relative;
    }
    .item-name .text-name a {
        color : #120C0D;
    }
    .item-name:hover .text-name a {
        color : #F03 !important;
        text-decoration: none !important;
    }
    .custom-dropdown-toggle {
        /* visibility: hidden; */
        transition: none;
        height: 32px;
        width: 32px;
        background-color: #F03;
        border: 1px solid #F03;
        padding: 8px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        color: #fff;
    }
    .custom-dropdown-menu a {
        padding: 8px 12px;
    }
    @media (max-width: 1366.98px) {
            .main-title {
                font-size: 24px;
            }

            .examination-title {
                font-size: 20px;
            }
            button.delete-items{
                width: 84px;
            }
            a.action-create{
                width: 129px;
            }

    }
    @media(min-width:429px) and (max-width: 834.98px) {
            .main-title {
                font-size: 20px;
                margin: 0;
            }
            .gap-search{
                flex-direction: column;
            }
            .filter-container .gap-search {
                height: 0;
                margin: 0;
            }
            .block-filter{
                position: relative;
                width: 100%;
                display: flex !important;
                height: 40px;

                .action-buttons-filter-extend{
                    position: absolute;
                    right: 50px;
                    height: 100%;
                    display: flex !important;
                }
            }
            .filter-hidden{
                display: none;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 12px;
            }
            .filter-hidden.show {
                display: block !important;
                margin-bottom: 40px;
                margin-top: 0;
            }
            .search-left {
                position: relative;
                top: -40px;
                width: 78%;
                display: block;
                margin-top: 0;

                .filter-hidden.show{
                    margin-top: 0;
                    top: -52px;
                    display: block;
                }
            }
            .search-right{
                position: relative;
                top: -68px;
            }
            .examination-title {
                font-size: 16px;
            }

            .status-select {
                width: 215px;
            }

            .examination-custom-select {
                width: 215px;
            }

            .examination-input-tree .custom-select {
                width: 215px;
            }
            .examination-title-action .gap-search{
                flex-direction: row;
            }
            .bot-search{
                flex-direction: row;
            }
            .reset-column.filter-hidden{
                margin: 0;
                position: relative;
                top: -40px;
                display: block;

                button{
                    position: absolute;
                    right: 0;
                    top: 0;
                }
            }
            .reset-column.filter-hidden.show{
                margin: 0;
                position: relative;
                top: -128px;

                button{
                    position: absolute;
                    right: 0;
                    top: 8px;
                }
            }
            .examination-title-action {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                gap: 10px;

                .gap-search{
                    display: flex;
                    flex-direction: row;
                    height: 32px;

                    button{
                        width: 68px;
                        padding: 8px !important;
                    }
                    a{
                        width: 107px;
                        padding: 8px !important;
                    }
                }
            }
            .inactive{
                min-width: 107px;
                max-width: 107px;
                padding: 0 6px !important;
                font-size: 12px !important;
            }
    }
    @media (max-width: 428.98px) {
        #page-wrapper #page {
            margin-top: 16px !important;
        }
        input[type="checkbox"]:not(.custom-control-input){
            min-height: 18px;
            min-width: 18px;
            max-width: 18px;
            max-height: 18px;
        }

        .gap-search{
            flex-direction: column;
        }
        .gap-search.filter-hidden{
            height: 0;
            margin: 0;
        }
        .gap-search.filter-hidden.show{
            height: 100%;
            margin: 0;
        }
        .block-filter{
            width: 100%;
            display: flex !important;
            height: 40px;
            margin-top: 71px;
            /* position: relative;
                top: 48px;
                margin-top: 8px; */
            .action-buttons-filter-extend{
                height: 100%;
            }
        }
        .examination-title{
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
            color: #120C0D
        }
        .examination-title-action {
            padding-top: 16px !important;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
            gap: 10px;
            
            .gap-search{
                display: flex;
                flex-direction: row;
                height: 32px;
                flex-wrap: nowrap;
                

                button{
                    width: 68px;
                    padding: 8px !important;
                }
                a{
                    width: 107px;
                    padding: 8px !important;
                }
            }
        }
        .container-input-search{
            position: relative;
            top: -91px;

            input::placeholder{
                font-size: 13px;
            }
        }
        .examination-input-tree,
        .status-select{
            width: calc(50% - 3px);
            max-width: calc(50% - 3px);
            margin-bottom: 0 !important;
        }
        .block-input-search{
            display: block !important;
        }
        .filter-hidden{
            display: none;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 12px;
        }
        .filter-hidden.show {
            display: block !important;
            margin-bottom: 16px;
        }
        .reset-column.filter-hidden {
            display: block;
            position: relative;
            top: -40px;
            left: 103px;
            margin: 0;
            width: fit-content;
        }
        .reset-column.filter-hidden.show {
            display: block;
            position: relative;
            top: -92px;
            left: 103px;
            margin: 0;
            width: fit-content;
            height: 0;
        }
        .search-left{
            margin: 0 !important;
            height: 0;
            display: block;
        }
        .search-right{
            display: unset;
        }
        .bot-search{
            flex-direction: row;
        }
        .tree-selector-wrapper {
            min-width: 0;
        }
        .examination-input-tree .custom-select,
        .examination-custom-select{
            width: 100%;
        }
        .examination-title{
            width: 45%;
        }
        .item-name.item-name-focus .order-up,
        .item-name.item-name-focus .order-down{
            display: none !important;
        }
        .item-name:hover .menu-action.order-up,
        .item-name:hover .menu-action.order-down{
            display: none !important;
        }
        .item-name.item-name-focus:hover .order-up,
        .item-name.item-name-focus:hover .order-down{
            display: none !important;
        }
        .inactive{
            min-width: 107px;
            max-width: 107px;
            padding: 0 6px !important;
            font-size: 12px !important;
        }
    }

    .last-sibling{
        position: absolute;
        right: 48px !important;
        margin-right: 8px !important;
    }
    /*  */
    .modal-overlay-custom {
        background-color: rgba(0, 0, 0, 0.5);
        padding-top: calc(20vh + 16px);
    }
    .examination-btn-reset {
        padding: 10px !important;
    }
     .item-name:hover button.custom-dropdown-toggle{
        display: flex !important;
        align-items: center;
        justify-content: center;
        padding-bottom: 5px;
        background-color: #F03 !important;
        border: none;
        color: #fff;
        visibility: visible;
        opacity: 1;
    }
    .item-name button.custom-dropdown-toggle.active {
        display: flex !important;
        align-items: center;
        justify-content: center;
        padding-bottom: 5px;
        background-color: #FED7DF !important;
        border: none;
        color: #FF0033 !important;
    }

     .item-name:hover .order-down,
     .item-name:hover .order-up{
        display: flex !important;
        align-items: center;
        justify-content: center;
    }

    .order-down,
    .order-up{
        display: none;
        height: 36px;
        width: 36px;
        background-color: #FED7DF;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        padding: 0px 3px;
        color: #FF0033;
    }
    .order-down{
        position: absolute;
        right: 48px;
        margin-right: 8px;
    }
    .order-up{
        position: absolute;
        right: 92px;
        margin-right: 8px;
    }
    .order-down span,
    .order-up span{
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .inactive{
        display: flex;
        color:#6F767E;
        min-height: 22px;
        max-height: 22px;
        padding: 4px 6px;
        justify-content: center;
        border: 1px solid #E0E0E0;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        align-items: center;
        gap: 10px;
        background: #EAEAEA;
        border-radius: 6px;
        border: 1px solid #E0E0E0;

        &:hover{
            color:#6F767E;
            background: #EAEAEA;
        }
    }

    .select-text{
        color: #44494D;
    }
    .examination-custom-select{
        color: #44494D;
    }
    .child-expanse-list{
        padding-left: 62px;
    }
    input[type="checkbox"]:not(.custom-control-input){
        width: 18px;
        height: 18px;
        border-radius: 4px;
        margin: 0 !important;
    }
    .show-children {
        width: 24px;
        align-items: center;
        justify-content: center;
        display: flex;
        height: 24px;
    }


    .item-name {
        position: relative;
        padding: 12px 8px;
        height: 56px;
    }
    .item-name-focus button.custom-dropdown-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
        padding-bottom: 5px;
        background-color: #F03 !important;
        border: none;
        color: #fff;
        visibility: visible;
        opacity: 1;
    }
    #page-local-workplce-training_program_categories .custom-dropdown-wrapper{
        opacity: unset;
        visibility: visible;
    }
    .examination-title-action {
        padding-top: 24px;
    }
}
.programs-form{
    display: grid;
    /* grid-template-columns: repeat(3, 1fr); */
    grid-template-columns: repeat(3, minmax(367px, 378px));
    grid-template-rows: repeat(2, 76px);
    gap: 24px;
    position: relative;
    margin-top: 24px;

    .form-group:not(.hidden) {
        display: flex;
        gap: 6px;
        flex-direction: column;
        flex-wrap: nowrap;
    }
    .form-group:nth-child(3){
        grid-column: 1;
        grid-row: 1;
        /* height: 76px; */
    }
    #fgroup_id_buttonar.femptylabel .felement .inner-reverse{
        display: grid !important;
        .mb-3.fitem{
            grid-column: 2;
            grid-row: 1;
            margin: 0 !important;
        }
        .mb-3.fitem.btn-cancel{
            grid-column: 1;
            grid-row: 1;
            margin: 0 !important;
        }
    }

    .form-group:nth-child(1){
        grid-column: 2;
        grid-row: 1;
        /* height: 76px; */
    }
    .form-group:nth-child(2){
        grid-column: 3;
        grid-row: 1;
        /* height: 76px; */
    }
    .fitem_id_organizationid{
        /* height: 76px; */
    }
    .fitem_id_displayorder{
        /* height: 76px; */
    }
    .fitem_id_status{
        /* height: 76px; */
    }
    .fitem_id_status select{
        color:#120C0D;
    }

    #fitem_id_description{
        width: 100%;
        grid-column: 1 / span 3;
        grid-row: 3;
    }
    #fitem_id_description .form-inline{
        max-width: 100%;
    }
    #fgroup_id_buttonar{
        grid-column: 3;
        grid-row: 4;
        padding: 0;
        margin-top: 8px;
    }
    .col-lg-9{
        max-width: 100% !important;
        flex: 0;
    }
    .col-lg-3{
        max-width: 100% !important;
        flex: 0;
    }
    .form-control{
        width: 100%;
    }
    .form-control::placeholder{
        color: #B5B4B4;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
    }
    .select-text{
        color: #4c5a73;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        position: relative;
        padding-right: 32px;
        background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        cursor: pointer;
        max-width: 100%;
    }

    .has-category .select-text,
    .has-org .select-text{
        color: #4c5a73;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        position: relative;
        padding-right: 32px;
        background-size: 12px;
        cursor: pointer;
        max-width: 100%;
        background-image: none;
    }

    .has-category .tree-clear-button{
        right: 0px;
    }
    .has-org .tree-clear-button{
        right: 30px;
    }
    .select-text.open {
        background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' xmlns='http://www.w3.org/2000/svg'%3E%3Cpolyline points='6 15 12 9 18 15'%3E%3C/polyline%3E%3C/svg%3E");
    }
    .custom-select{
        width: 100%;
    }
    .edw-icon-Info{
        font-family: inherit !important;
        color: red !important;
    }
    .edw-icon-Info::before {
        content: '*';
        font-family: inherit !important;
        font-size: 1rem;
        font-weight: bold;
        color: red;
    }
    input[name="cancel"] {
        padding: 12px 16px;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0%;
        color: #44494D;
        background: #E0E0E0;
    }
    input[name="submitbutton"] {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        color: #FFFFFF;
        padding: 12px 30px;
    }
}
.programs-form.compact {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 60px);
    gap: 12px;
    position: relative;
    margin-top: 24px;

    .code_id input,
    .name_id input,
    .parent_id input,
    .org_id input,
    .displayorder_id input,
    .description_id textarea{
        background: none;
        border-radius: unset;
        border: none;
        padding: 0;
        margin-top: 6px;
        height: auto !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        color: #120C0D;
        line-height: 24px;
        cursor: not-allowed;

        &:focus{
            box-shadow: none !important;
            pointer-events: none;
            cursor: not-allowed;
        }
        &::placeholder{
            color: transparent;
        }
    }
    .description_id textarea{
        resize: none;
    }
    .code_id label,
    .name_id label,
    .parent_id label.edw-form-label,
    .org_id label,
    .displayorder_id label,
    .status_id label{
        color: #6F767E;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
    }
    #fitem_id_cancel{
        width: 100%;
        grid-column:3 !important;
        grid-row: 4;

        .felement{
            display: flex;
            justify-content: end;
        }
    }
    #fitem_id_parent_name.parent_id{
        grid-column: 1 !important;
        grid-row: 1;
        /* height: 76px; */
    }
    #fitem_id_name.form-group.name_id{
        grid-column: 3;
        grid-row: 1;
        /* height: 76px; */
    }
    #fitem_id_code.form-group.code_id{
        grid-column: 2 !important;
        grid-row: 1;
    }
}

#page-local-workplace-edit_programs .examination-custom-form .form-programs .form-group .col-12 {
    flex: 0;
}
#page-local-workplace-edit_training_program_category .programs-form .col-12 {
    flex: 0;
}

body.dark-mode#page-local-workplace-training_program_categories .svg-file-light{
    display: none;
}
body.dark-mode#page-local-workplace-training_program_categories .svg-file-dark{
    display: flex !important;
}
/* programs */
.form-programs{
    display: grid;
    grid-template-columns: repeat(3, minmax(378px, 410px));
    /* grid-template-rows: repeat(2, 76px); */
    grid-auto-rows: minmax(76px, auto);
    gap: 24px;
    position: relative;
    margin-top: 24px;
    .form-group:not(:last-child){
        margin: 0;
    }
    .form-group:not(.hidden) {
        display: flex;
        gap: 6px;
        flex-direction: column;
        flex-wrap: nowrap;
    }
    #fitem_id_category_id{
        grid-column: 3;
        grid-row: 1;
        /* height: 76px; */
    }
    #fitem_id_name{
        grid-column: 2;
        grid-row: 1;
        /* height: 76px; */
    }
    #fitem_id_code{
        grid-column: 1;
        grid-row: 1;
        /* height: 76px; */
    }
    #fitem_id_category_name textarea#id_category_name{
        width: 100% !important;
    }
    .fitem_id_organizationid{
        /* height: 76px; */
    }
    .fitem_id_displayorder{
        /* height: 76px; */
    }
    #fitem_id_status{
        width: 100%;
        /* height: 76px; */
        grid-row: 4;
        margin: 0;
    }
    .fitem_id_image{
        width: 100%;
        /* height: 76px; */
        grid-row: 4;
        grid-column: 1 / span 3;
    }
    #fitem_id_department_id,
    #fitem_id_departmentname{
        width: 100%;
        height: 47px;
        grid-column: 1;
        grid-row: 2;
    }
    .time{
        display: flex;
        /* align-items: center; */
        gap: 24px;
        width: 100%;
        grid-column: 2/  4;
        grid-row: 2;
        /* max-width: none; */
    }
    .time #fitem_id_starttime.calendar-input,
    .time #fitem_id_endtime.calendar-input{
        margin: 0 !important;
        /* height: 79px !important; */
        min-width: 378px !important;
        max-width: 394px !important;
        background: none;

        input.form-control{
            background: url(/local/workplace/pix/icon/calendar.svg) no-repeat right 10px center;
            background-size: 20px 20px;
            background-position: calc(100% - 14px) 55%;
            cursor: pointer;
        }
    }
    .error-validate{
            border: 1px solid #dc3545 !important;
    }
}
.main-content-full.program .filter-row .reset-button:hover {
        background-color: #F03;
    }
.main-content-full.program .filter-row .reset-button:hover svg path {
    stroke: #FFF;
}
/*  */
#page-local-workplace-programs {
    .reset-button:focus{
        background-color: #F03;
            svg path{
                stroke: #fff;
            }
    }
    .custom-height{
        height: 530px;
    }
    .empty-state{
        display: flex;
        flex-direction: column;
        max-width: 160px;
        margin: auto;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        img{
            max-width: 126px;
            margin: 0 auto;
        }
    }
    .empty-state.program{
        transform: translate(84%, -4%);
        height: 186px;
        img{
            height: 162px;
        }
    }
    .column-name:focus-within {
        border-color: transparent !important;
        box-shadow: 0 0 0 1px #f03 !important;
    }
    .show-children {
        width: 24px;
        align-items: center;
        justify-content: center;
        display: flex;
        height: 24px;
    }
    .name.unactive {
        color: #6F767E;
    }
    .line-through {
        color: #6F767E;
        font-style: italic;
        /*text-decoration-thickness: 1px !important;*/
    }
    .right-main {
        width: 68%;
        max-height: unset;
    }
    .reset-button:hover {
        background-color: #F03;
    }
    .reset-button:hover svg path {
        stroke: #FFF;
    }
    .reset-button svg {
        width: 20px;
        height: 20px;
    }
    .title-programs {
        height: 40px;
        font-weight: 600;
        font-size: 20px;
        color: #120C0D;
        margin-top: 32px;
        margin-bottom: 20px;
    }
    .title-text-periods{
        font-size: 16px;
    }
    .block-filter{
        display: none;
    }
    @media (max-width: 1366.98px) {
        .main-title {
            font-size: 24px;
        }
        .right-main {
            width: 63%;
        }
        .title-text-periods{
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }
        .action-buttons button{
            width: 84px;
        }
        #create-program-btn{
            width: 129px;
        }
    }
    @media (max-width: 834px) {
        .d-flex.main-programs {
            flex-direction: column;
            gap: 0;
        }
        .custom-pagination-container.table-pagination {
            margin-top:12px;
        }
        .left-main {
            width: 100%;
            .question-categories-container.cat-responsive-md {
                max-height: 266px !important;
                width: 100%;
            }
        }
        .right-main {
            width: 100%;
        }
        .title-programs {
            font-size: 18px;
            margin-top: 24px;
            margin-bottom: 12px;
        }
        .main-title {
            font-size: 20px;
            min-height: fit-content;
        }
    }
    @media (min-width:429px) and (max-width: 834.98px) {
        #page.drawers .main-inner {
            margin-top: 24px;
        }
        .d-flex.main-programs {
            flex-direction: column;
            gap: 0;
        }
        .custom-pagination-container.table-pagination {
            margin-top:12px;
        }
        .dropdown.time {
            margin-top: 48px;
        }
        .column-org.examination-input-tree,
        .column-status,
        .courses-time-dropdown-toggle{
            width: calc(50% - 8px);
            max-width: calc(50% - 8px);
        }
        .column-status .examination-custom-select ,
        .column-org .custom-select{
            width: 100%;
        }
        .right-main {
            width: 100%;
        }
        .title-programs {
            font-size: 18px;
            margin-top: 24px;
            margin-bottom: 12px;
        }
        .main-title {
            font-size: 20px;
            min-height: fit-content;
        }
        .filter-row .column-name{
            flex:none;
        }
        .reset-column{
            display: none;
        }
        .reset-column2{
            display: block !important;
        }
        @media (min-width : 767px){
            #sidebarTraining{
                position: fixed;
                z-index: 9999;
                top: 198px;
                left:64px;
                display: flex !important;
                padding: 6px 8px;
                align-items: center;
                gap: 8px;
                border-radius: 0px 16px 16px 0px;
                border: none;
                background: #F03;
            }
        }
        @media (max-width: 766.98px){
            #sidebarTraining{
                position: fixed;
                z-index: 9999;
                top: 222px;
                left:0px;
                display: flex !important;
                padding: 6px 8px;
                align-items: center;
                gap: 8px;
                border-radius: 0px 16px 16px 0px;
                border: none;
                background: #F03;
            }
        }
        .left-main {
            position: fixed;
            top: 52px;
            left: 0px;
            width: 632px;
            height: 100%;
            max-height: 1194px;
            background: #fff;
            color: white;
            padding: 12px;
            box-sizing: border-box;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 9999;
            display: block;
            border-radius: 0 12px 12px 0 ;
            border-right: 1px solid #E0E0E0;
            border-bottom: 1px solid #E0E0E0;
        }
        .left-main.active {
            transform: translateX(0);

            .title-programs{
                color: #44494D;
                padding: 0 !important;
                margin: 0;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: 28px; display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .top-bar{
                height: 4px;
                background: #F2F2F2;
                margin: 16px 0 24px 0;
            }
            .top-bar-color{
                width: 92px;
                display: block;
                height: 4px;
                background: #f03;
            }
            .card, .question-categories-container.cat-responsive-md{
                max-height: 1086px !important;
                height: 100%;
            }
            .close-left-main{
                display: flex !important;
                width: 32px;
                height: 32px;
                padding: 8px;
                justify-content: center;
                align-items: center;
                background: #F2F2F2;
                border-radius: 50%;
            }

            .question-categories-container .name{
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                color:#120C0D;
                padding: 0;
            }
            .nav-link-periods{
                width: 342px !important;
                max-width: 342px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .line-through {
                span, path{
                    color : #6F767E !important;
                    fill:#B5B4B4;
                }
            }

        }
        #create-program-btn,
        .examination-btn-height-custom{
            padding: 8px 12px;
            height: 40px;
        }
        button.examination-btn-height-custom{
            width: 84px;
        }
        a.examination-btn-height-custom{
            width: 129px;
        }
    }
    @media (max-width: 428px) {
        #page-wrapper #page {
            /* margin-top: 16px !important; */
        }
        .block-filter{
            width: 100%;
            display: flex !important;
            height: 40px;

            .action-buttons-filter-extend{
                height: 100%;
            }
        }
        .filter-hidden {
            display: none;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 12px;
        }

        .filter-hidden.show {
            display: block !important;
            margin-bottom: 16px;
        }
        .filter-hidden.show .courses-time-dropdown-toggle{
            display: flex !important;
            /* margin-bottom: 16px; */
            width: calc(50% - 8px);
            max-width: calc(50% - 8px);

        }
        .dropdown.time {
            position: relative;
            top: 28px;
            display: none;
            gap: 15px;
        }
        .dropdown.time.filter-hidden.show {
            position: relative;
            padding-bottom: 10px;
            top: 76px;
            display: flex !important;
        }
        .courses-time-dropdown-toggle {
            margin: 0;
            width: calc(50% - 6px);
            max-width: calc(50% - 6px);
        }
        .title-programs {
            font-size: 16px;
            margin-top: 16px;
            height: unset;
        }
        .main-title {
            font-size: 16px;
        }
        .filter-row{
            column-gap: 15px;
            row-gap: 8px;
        }
        .filter-row .column-name{
            flex:none;
        }
        .column-org.examination-input-tree,
        .column-status{
            margin: 0;
            width: calc(50% - 8px);
            max-width: calc(50% - 8px);
        }
        .tree-selector-wrapper,
        .examination-custom-select{
            width: 100%;
            min-width: unset;
        }
        .reset-column.filter-hidden{
            display: block;
            position: relative;
            top: -48px;
            left: 107px;
            margin: 0;
        }
        .reset-column.filter-hidden.show{
            display: block;
            position: relative;
            top: -112px;
            left: 107px;
            margin: 0;
        }
        .reset-column2.filter-hidden{
            display: none !important;
        }
        /* .reset-column2.filter-hidden{
            position: relative;
            display: block !important;
            top: -60px;
            left: 107px;
        }
        .reset-column2.filter-hidden.show{
            position: relative;
            top: -160px;
            left: 107px;
        } */
        #sidebarTraining{
            position: fixed;
            z-index: 9999;
            top: 222px;
            left: 0;
            display: flex !important;
            padding: 6px 8px;
            align-items: center;
            gap: 8px;
            border-radius: 0px 16px 16px 0px;
            border: none;
            background: #F03    ;
        }
        .left-main {
            position: fixed;
            top: 52px;
            left: 0;
            width: 100%;
            height: 100%;
            max-height: 926px;
            background: #fff;
            color: white;
            padding: 12px;
            box-sizing: border-box;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            z-index: 9999;
            display: block;
            border-radius: 0 12px 12px 0 ;
            border-right: 1px solid #E0E0E0;
            border-bottom: 1px solid #E0E0E0;
        }
        .left-main.active {
            transform: translateX(0);
            width: 364px;
            max-width: 364px;
            min-width: 364px !important;
            .title-programs{
                color: #44494D;
                padding: 0 !important;
                margin: 0;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: 28px; display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .top-bar{
                height: 4px;
                background: #F2F2F2;
                margin: 16px 0 24px 0;
            }
            .top-bar-color{
                width: 51px;
                display: block;
                height: 4px;
                background: #f03;
            }
            .card, .question-categories-container.cat-responsive-md{
                max-height: 825px !important;
                height: 100%;
            }
            .close-left-main{
                display: flex !important;
                width: 32px;
                height: 32px;
                padding: 8px;
                justify-content: center;
                align-items: center;
                background: #F2F2F2;
                border-radius: 50%;
            }

            .question-categories-container .name{
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                color:#120C0D;
                padding: 0;
            }
            .nav-link-periods{
                width: 342px !important;
                max-width: 342px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .line-through {
                span, path{
                    color : #6F767E !important;
                    fill:#B5B4B4;
                }
            }
        }
        .right-main{
            position: relative;
            top: 48px;

            .action-buttons{
                display: flex;
                align-items: center;

                button{
                    color: #FF859F;
                    padding: 8px !important;
                    height: 34px;
                    font-size: 14px !important;
                    font-weight: 500;
                    line-height: 22px; /* 157.143% */
                    letter-spacing: -0.14px;
                    border: none;
                }
                .img-div{
                    display: flex;
                    align-items: center;
                    width: 20px;
                    height: 20px;
                }
                #create-program-btn{
                    height: 32px;
                    padding: 8px !important;
                    font-size: 14px !important;
                    font-weight: 500;
                    line-height: 22px; /* 157.143% */
                    letter-spacing: -0.14px;
                    border: none;
                    width: 107px;
                }

            }
        }
        .examination-controls{
            margin: 20px 0;
        }

    }
}
body.dark-mode#page-local-workplace-programs .svg-file-light{
    display: none;
}
body.dark-mode#page-local-workplace-programs .svg-file-dark{
    display: flex !important;
}
/*page-local-workplace-edit_programs */
#page-local-workplace-edit_programs{
    #page #topofscroll>div.container{
        padding: 0 24px !important;
    }
    .placeholder-text{
        color: #B5B4B4 !important;
    }
    .form-programs{
        .time #fitem_id_starttime #id_starttime,
        .time #fitem_id_endtime #id_endtime{
            cursor: pointer;
        }
        .time #fitem_id_starttime .invalid-feedback{
            font-size: 12.8px;
        }
        .calendar-input .form-control:focus{
            border-color: #f03 !important;
        }
        .time #fitem_id_endtime .invalid-feedback{
            font-size: 13px;

            :focus{
                    border-color: #f03;
            }
        }
        #fgroup_id_estimated_cost{
        /* height: 76px; */
            margin: 0;
        }
        #fitem_id_image {
            width: 100%;
            grid-column: 1/ span 3;
            grid-row: 4;
            margin: 0;
            max-width: none;
        }
        /* .img-focus#fitem_id_image{
            grid-row: 4 !important;
        } */
        .flex-gap-1{
            gap: 0;
        }
        #fitem_id_description{
            width: 100%;
            grid-column: 1 / span 3;
            grid-row: 5;
            max-width: none;
        }
         #fitem_id_description.desc-focus{
            grid-row: 5 !important;
        }
        #fitem_id_image_preview{
            grid-row: 4;
        }
        #fitem_id_cancel{
            width: 100%;
            grid-column: 1 / span 3;
            grid-row: 6;

            .felement{
                display: flex;
                justify-content: end;
            }
        }
        #fitem_id_description .form-inline{
            max-width: 100%;
        }
        #fgroup_id_buttonar{
            grid-column: 3;
            grid-row: 6;
            padding: 0;
            margin-top: 8px;
        }
        .col-lg-9{
            max-width: 100% !important;
            flex: 0;
        }
        .col-lg-3{
            max-width: 100% !important;
            flex: 0;
        }
        .form-control{
            width: 100%;
        }
        .form-control::placeholder{
            color: #B5B4B4;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }
        .select-text{
            color: #4c5a73;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }
        .custom-select{
            width: 100%;
        }
        .edw-icon-Info{
            font-family: inherit !important;
            color: red !important;
        }
        .edw-icon-Info::before {
            content: '*';
            font-family: inherit !important;
            font-size: 1rem;
            font-weight: bold;
            color: red;
        }
        input[name="cancel"] {
            padding: 12px 16px;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: 0%;
            color: #44494D;
            background: #E0E0E0;
        }
        input[name="submitbutton"] {
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: #FFFFFF;
            padding: 12px 30px;
        }
        .edw-form-label,
         #fitem_id_image_preview span {
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #120C0D;
        }
        #fitem_id_image_preview{
            grid-column: 1/ span 3;
        }
        .image_preview{
            width: 100%;
            grid-column: 1/ span 3;
            grid-row: 5;
            margin: 0;
        }

        #fitem_id_image_preview .form-control-static {
            width: 100%;
        }
        #fitem_id_image_preview .form-control-static img {
            width: 100%;
            max-width: 100% !important;
            height: 158px;
            object-fit: center;
        }
        #fitem_id_category_name.catelog_id{
            grid-column: 3;
            grid-row: 1;
        }
        #fitem_id_code.code_id{
            grid-column: 1;
            grid-row: 1;
        }
        .name_id{
            grid-column: 2;
            grid-row: 1;
        }
        .time_id input#id_starttime,
        .time_id input#id_endtime {
            padding: 0;
            border: none;
            color: #120C0D;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            cursor: not-allowed !important;
        }
        .name_id,
        .code_id,
        .catelog_id,
        .org_id,
        .estimated_cost_id,
        .status_id,
        .description_id,
        #fitem_id_image_preview{
            height: auto;
            margin: 0;
        }
        .name_id label,
        .code_id label,
        .catelog_id label,
        .org_id label,
        .estimated_cost_id label,
        .status_id label,
        .description_id label,
        .certificates_id .edw-label,
        .required_id .edw-label,
        .time_id .edw-form-label,
        #fitem_id_image_preview span {
            color: #6F767E;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
        }
        .name_id input,
        .code_id input,
        .catelog_id input,
        .org_id input,
        .estimated_cost_id input,
        .mform .form-inline .fdate_time_selector,
        .certificates_id .text-paragraph,
        .required_id .text-paragraph,
        .description_id textarea{
            background: none;
            border-radius: unset;
            border: none;
            padding: 0;
            margin-top: 6px;
            height: auto !important;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            color: #120C0D;
            line-height: 24px;
            cursor: not-allowed;

            &:focus{
                box-shadow: none !important;
                pointer-events: none;
                cursor: not-allowed;
            }
            &::placeholder{
                color: transparent;
            }
        }
        .certificates_id input,
        .required_id input{
            display: none !important;
        }
        .certificates_id .text-paragraph,
        .required_id .text-paragraph{
            margin: 0 !important;
        }
        #fitem_id_image_preview img{
            border-radius: 8px;
        }
        .description_id textarea{
            resize: none;
        }
    }
    .form-programs.view_form_programs{
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 60px);
        gap: 24px;
        position: relative;
        margin-top: 24px;

        input[type="checkbox"]{
            display: none !important;
        }
        .text-paragraph {
            margin-left: 0 !important;
        }
        #fitem_id_starttime,
        #fitem_id_endtime{
            min-width: 0 !important;
        }
    }
    @media (max-width : 428.98px){
        #page-wrapper #page .main-inner{
            margin-top: 50px !important;
        }
        .form-programs {
            display: grid !important;
            grid-template-columns: repeat(1, minmax(0, 396px)) !important;
            grid-template-rows: repeat(2, 76px);
            gap: 24px;
            position: relative;
            margin-top: 24px;
        }
        .form-programs #fitem_id_name {
            grid-row: 2;
            grid-column: 1;
            /* height: 76px; */
        }
        .form-programs .block_categoryid{
            grid-row: 3;
            grid-column: 1;
            margin-bottom: 0;

        }
        .form-programs #fitem_id_department_id{
            grid-row: 4;
            grid-column: 1;

        }
        .form-programs .time{
            grid-row: 5;
            grid-column: 1;
            flex-direction: column;

            #fitem_id_starttime.calendar-input{
                min-width: 0 !important;
                max-width: 380px !important;
            }
            #fitem_id_endtime.calendar-input{
                min-width: 0 !important;
                max-width: 380px !important;
            }
        }
        .form-programs #fgroup_id_estimated_cost{
            grid-row: 6;
            grid-column: 1;
        }
        .form-programs .edw-form-label.certificates{
            grid-row: 7;
            grid-column: 1;
        }
        .form-programs .edw-form-label.required{
            grid-row: 8;
            grid-column: 1;
        }
        .form-programs #fitem_id_status {
            grid-row: 9;
            grid-column: 1;
        }
         .form-programs #fitem_id_image.img-focus {
            grid-row: 9 !important;
            grid-column: 1;
        }
        .form-programs #fitem_id_image {
            grid-row: 10;
            grid-column: 1;
        }
        .form-programs #fitem_id_description.desc-focus {
            grid-row: 7 !important;
        }
        .form-programs #fitem_id_description {
            grid-row: 11;
            grid-column: 1;
        }
        .form-programs #fgroup_id_buttonar{
            grid-row: 12;
            grid-column: 1;
        }

        
        #page-wrapper #page{
            margin-top: 16px !important;
        }
        .invalid-feedback{
            position: unset;
        }
        .select2-selection__arrow{
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 9L12.333 15L18.333 9' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") !important;
            background-repeat: no-repeat;
            padding-right: 0 !important;
            height: 20px;
            width: 20px;
        }
        .select2-container--open .select2-selection__arrow{
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 15L12.333 9L18.333 15' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E")  !important;
            background-repeat: no-repeat;
            padding-right: 0 !important;
            height: 20px;
            width: 20px;
        }
        .form-programs input::placeholder,
        .form-programs input,
        .placeholder-text .select2-selection__rendered,
        .select2-selection__rendered,
        .select-text,
        .custom-select#id_unit,
        .custom-select#id_status,
        textarea{
            font-size: 14px;
        }
        .custom-file-preview{
            /* justify-content: inherit;
            gap: 0; */
            img{
                width: 48px;
                max-width: 48px;
            }
            .preview-info{
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 244px;
            }
            button{
                padding: 0;
            }
        }
        input#id_cancel{
            width: 75px;
            height: 40px;
            padding: 8px 12px;
        }
        input#id_submitbutton{
            width: 84px;
            height: 40px;
            padding: 8px 12px;
        }
        .form-programs.view_form_programs{
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            grid-template-rows: repeat(2, 52px);
            row-gap: 16px;
            column-gap: 24px;
            position: relative;
            margin-top: 16px;

            input,
            textarea{
                font-size: 14px !important;
            }
            input[type="checkbox"]{
                display: none !important;
            }
            .form-group:not(.hidden){
                gap: 8px;
            }
            .form-control{
                margin: 0;
            }
            #fitem_id_name{
                grid-row: 1;
                grid-column: 2;
            }
            #fitem_id_category_name{
                grid-row: 2;
                grid-column: 1;
            }
            #fitem_id_departmentname{
                height: unset;
                grid-row: 2;
                grid-column: 2;
            }
            .time.time_id{
                grid-row: 3;
                grid-column: 1 / span 2;
                flex-direction: row;
                height: unset;

                #fitem_id_starttime,
                #fitem_id_endtime{
                    min-width: 0 !important;
                }
                #id_starttime,
                #id_endtime{
                    height: 22px;
                }

            }
            #fgroup_id_estimated_cost{
                grid-row: 4;
                grid-column: 1;
                height: 52px !important;
                .felement{
                    max-height: 22px;
                }
                .felement .fitem{
                    margin: 0;
                    height: 22px;

                    span{
                        color: #6F767E;
                    }
                }
            }
            .edw-label{
                margin: 0;
            }
            .certificatescertificates_id{
                grid-row: 4;
                grid-column: 2;
            }
            .requiredrequired_id{
                grid-row: 5;
                grid-column: 1;
            }
            input[type="checkbox"]{
                width: 18px;
                height: 18px;
                max-width: 18px;
                max-height: 18px;
                margin: 0 !important;
            }
            .text-paragraph{
                color : #120C0D;
                font-weight: 600;
                line-height: 22px;
                margin: 0 !important;
            }
            #fitem_id_status{
                grid-row: 5;
                grid-column: 2;

                .felement{
                    font-size: 14px ;
                    color: #120C0D;
                    font-weight: 600;
                    line-height: 22px;
                }
            }
            .image_preview{
                grid-row: 6;
                grid-column: 1 / span 2;
            }
            #fitem_id_description{
                grid-row: 7;
                grid-column: 1 / span 2;
            }
            #fitem_id_cancel{
                grid-row: 8;
                grid-column: 2;
            }
            .edw-form-label{
                font-weight: 400;
                font-size: 14px;
                line-height: 22px;
                color: #6F767E;
            }
    }
}  
    }
    @media (min-width:429px) and (max-width:834.98px){
        #page-wrapper #page{
            margin-top: 40px !important;
        }
        .form-programs {
            display: grid !important;
            grid-template-columns: repeat(2, minmax(0, 349px)) !important;
            grid-template-rows: repeat(2, 68px);
            gap: 24px;
            position: relative;
            margin-top: 24px;

            .col-form-label{
                height: 22px;

                .inner{
                    height: 100%;
                }

                .text-danger{
                    height: 22px;
                }
            }
            .custom-select#id_unit{
                height: 40px;
                width: 70px !important;
            }
            input::placeholder,
            input,
            .placeholder-text .select2-selection__rendered,
            .select2-selection__rendered,
            .select-text,
            .custom-select#id_unit,
            .custom-select#id_status,
            textarea{
                font-size: 14px !important;
            }
            .felement{
                input.form-control{
                    max-height: 40px;
                    min-height: 40px;
                    padding: 9px 12px;
                }
            }
            #fitem_id_code{
                grid-row: 1;
                grid-column: 1;
            }
            #fitem_id_name{
                grid-row: 1;
                grid-column: 2;
            }
            #fitem_id_category_id{
                grid-row: 2;
                grid-column: 1;
            }
            #fitem_id_department_id{
                grid-row: 2;
                grid-column: 2;

                .tree-selector-wrapper{
                    min-width: 0;
                }
            }
            .time {
                grid-row: 3;
                grid-column: 1 / span 2;
            }
            #fgroup_id_estimated_cost{
                grid-row: 4;
                grid-column: 1;

                .inner-reverse{
                    max-height: 40px;
                }
            }
            .time #fitem_id_starttime, .time #fitem_id_endtime {
                /* height: 79px !important; */
                max-width: 349px !important;
                min-width: 0 !important;
            }
            .edw-form-label.certificates{
                grid-row: 4;
                grid-column: 2;
            }
            #fitem_id_status{
                grid-row: 5;
                grid-column: 1;
                .custom-select{
                    padding: 9px 12px;
                    height: 40px !important;
                }
            }
            .edw-form-label.required {
                grid-row: 5;
                grid-column: 2;
            }
            #fitem_id_image{
                grid-row: 6;
                grid-column: 1 / span 2;
            }
            #fitem_id_description{
                grid-row: 7;
                grid-column: 1 / span 2;
            }
            #fgroup_id_buttonar{
                grid-row: 8;
                grid-column: 2;

                input#id_cancel{
                    width: 75px;
                    height: 40px;
                    padding: 8px 12px;
                    font-size: 16px !important;
                }
                input#id_submitbutton{
                    width: 84px;
                    height: 40px;
                    padding: 8px 12px;
                    font-size: 16px !important;

                }
            }
            .text-paragra{
                color: #44494D;
            }
            .select2-container{
                min-height: 40px;
                max-height: 40px;

                .select2-selection{
                    min-height: 40px;
                    max-height: 40px;
                }
            }
            .tree-selector-wrapper{
                max-height: 40px;
                min-height: 40px;
                padding: 9px 12px;
            }
        }
        .form-programs.view_form_programs{
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            grid-template-rows: repeat(2, 60px);
            gap: 24px;
            position: relative;
            margin-top: 16px;

            .form-group:not(.hidden) {
                gap: 12px;
            }
            .felement{
                input.form-control{
                    margin: 0;
                    padding: 0;
                    height: 26px !important;
                    min-height: 0;
                    max-height: 28px;
                }
            }

            input[type="checkbox"]{
                width: 18px;
                height: 18px;
                max-width: 18px;
                max-height: 18px;
                display: none !important;
            }

            input,
            .placeholder-text .select2-selection__rendered,
            .select2-selection__rendered,
            .select-text,
            .custom-select#id_unit,
            .custom-select#id_status,
            textarea{
                font-size: 16px !important;
            }
            .edw-form-label{
                color: #6F767E;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
            }
            .text-paragraph{
                color: #120C0D;
                font-size: 16px;
                font-weight: 500;
                line-height: 24px;
                margin: 0 !important;

            }
            #fitem_id_code{
                grid-row: 1;
                grid-column: 1;

                .felement{
                    max-height: 26px;
                    height: 26px;
                }
            }
            #fitem_id_name{
                grid-row: 1;
                grid-column: 2;
            }
            #fitem_id_category_name{
                grid-row: 1;
                grid-column: 3;
            }
            #fitem_id_departmentname{
                grid-row: 2;
                grid-column: 1;
                height: unset;
            }
            .time.time_id{
                grid-row: 2;
                grid-column: 2/ 4;
                height: 60px;
                #fitem_id_starttime,
                #fitem_id_endtime {
                    height: 60px !important;    
                    max-width: 224px !important;
                }
            }
            #fgroup_id_estimated_cost{
                grid-row: 3;
                grid-column: 1;
                height: 60px !important;
                .felement{
                    max-height: 26px;
                }
                .felement .fitem{
                    margin: 0;
                    height: 26px;

                    span{
                        color: #6F767E;
                    }
                }
            }
            .certificatescertificates_id{
                grid-row: 3;
                grid-column: 2;
                height: 60px;

                .form-group{
                    height: 26px;
                    gap: 0;
                }
            }
            .requiredrequired_id{
                grid-row: 3;
                grid-column: 3;
                height: 60px;

                .form-group{
                    height: 26px;
                    gap: 0;
                }
            }
            .edw-label{
                margin-bottom: 12px;
                height: 22px;
            }
            #fitem_id_status{
                grid-row: 4;
                grid-column: 1;

                .felement{
                     color: #120C0D;
                font-size: 16px;
                font-weight: 500;
                line-height: 24px;
                }
            }
            .image_preview.imgpr-focus{
                grid-row: 4 !important;
                grid-column: 1 / span 3;
            }
            .imgpr-focus{
                grid-row: 4;
                grid-column: 1 / span 3;
            }
            #fitem_id_description{
                grid-row: 6;
                grid-column: 1 / span 3;
            }
            #fitem_id_cancel{
                grid-row: 7;
                grid-column: 3;

                #id_cancel{
                    width: 82px;
                    height: 40px;
                    padding: 8px 12px;
                }
            }
        }
        #page.drawers .main-inner {
            margin-top: 24px;
            /* margin-bottom: 16px; */
        }
        .examination-quiz-breadcrumb{
            padding-bottom: 0 !important;
        }
    }
    @media (min-width:835px) and (max-width:1366.98px){
        #page.drawers .main-inner {
            margin-top: 32px;
        }
        h2{
            font-size: 24px;
            line-height: 32px;
            margin: 0;
        }
        .form-programs {
            display: grid !important;
            grid-template-columns: repeat(3, minmax(209px, 330px)) !important;
            grid-template-rows: repeat(2, 76px);
            gap: 24px;
            position: relative;
            margin-top: 32px;

            .col-form-label{
                height: 22px;

                .inner{
                    height: 100%;
                }

                .text-danger{
                    height: 22px;
                }
            }

            .custom-select#id_unit{
                height: 48px;
                width: 70px !important;
            }

            input::placeholder{
                font-size: 14px;
            }
            input[type="checkbox"]{
                width: 18px;
                height: 18px;
            }
            input,
            .placeholder-text .select2-selection__rendered,
            .select2-selection__rendered,
            .select-text,
            .custom-select#id_unit,
            .custom-select#id_status,
            textarea{
                color: #120C0D;
            }
            .time #fitem_id_starttime,
            .time #fitem_id_endtime {
                margin: 0 !important;
                height: 76px !important;
                min-width: 0 !important;
                max-width: 314px !important;
            }

            #fitem_id_name,
            #fitem_id_code{
                height: 76px !important;
                margin-bottom: 0;
            }
            #fitem_id_department_id{
                .tree-selector-wrapper{
                    min-width: 0 !important;
                }
                .tree-selector-wrapper::after {
                    content: '';
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%) rotate(0deg); /* mũi tên xuống */
                    width: 24px;
                    height: 24px;
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 9L12.333 15L18.333 9' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
                    background-repeat: no-repeat;
                    background-size: contain;
                    pointer-events: none;
                }
                .tree-selector-wrapper[data-open="true"]::after {
                transform: translateY(-50%) rotate(180deg);
                }
            }
            #fitem_id_status{
                .custom-select{
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 9L12.333 15L18.333 9' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
                    background-repeat: no-repeat;     
                    background-size: 24px;  
                }
            }
            #fgroup_id_estimated_cost{
                height: 76px !important;

                .inner-reverse{
                    max-height: 48px;
                    gap: 6px;
                }
                .mb-3.fitem{
                    /* margin: 0 auto !important; */
                }
            }
            
            .text-paragraph{
                color:#44494D;
            }
            .form-check-input {
                margin: 0 !important;
            }
            #fgroup_id_buttonar{
                grid-row: 7;

                input#id_cancel{
                    width: 75px;
                    height: 40px;
                    padding: 8px 12px;
                    font-size: 16px !important;
                }
                input#id_submitbutton{
                    width: 84px;
                    height: 40px;
                    padding: 8px 12px;
                    font-size: 16px !important;

                }
            }
                .select2-selection__arrow{
                    padding-right: 0;
                    width: 24px;
                    max-width: 24px;
                    height: 24px;
                }

        }
        .form-programs.view_form_programs{
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            grid-template-rows: repeat(2, 60px);
            gap: 24px;
            position: relative;
            margin-top: 16px;

            /* .form-group:not(.hidden) {
                gap: 12px;
            } */
            input[type="checkbox"]{
                display: none !important;
            }
            #fitem_id_name, #fitem_id_code{
                height: 60px !important;
            }
            #fgroup_id_estimated_cost {
                height: 60px !important;

                .inner-reverse {
                    max-height: 24px;
                    gap: 6px;
                }
            }
            .edw-form-label, .edw-label{
                font-size: 16px;
                color: #6F767E;
                font-weight: 400;
            }
            .text-paragraph{
                font-size: 16px;
                color: #120C0D;
                font-weight: 500;
            }
            .certificatescertificates_id,
            .requiredrequired_id{
                .fitem{
                    height: 24px;
                    gap:0;
                }
            }
            #fitem_id_status{
                .felement{
                    color: #120C0D;
                    font-size: 16px;
                    font-weight: 500;
                }
            }
            #id_cancel{
                width: 82px;
                height: 40px;
                padding: 8px 12px;
            }
            .text-paragraph{
                color:#44494D;
                margin: 0 !important;
            }
        }
    }
    @media (min-width:1367px) and (max-width:1920.98px){
        #page.drawers .main-inner {
            margin-top: 32px;
        }
        .form-programs {
            display: grid;
            grid-template-columns: repeat(3, minmax(378px, 410px));
            /* grid-template-rows: repeat(2, 76px); */
            grid-auto-rows: minmax(76px, auto);
            gap: 24px;
            position: relative;
            margin-top: 32px;

            input,
            .placeholder-text .select2-selection__rendered,
            .select2-selection__rendered,
            .select-text,
            .custom-select#id_unit,
            .custom-select#id_status,
            textarea{
                color: #120C0D;
            }

            .time #fitem_id_starttime,
            .time #fitem_id_endtime {
                margin: 0 !important;
                /* height: 79px !important; */
                min-width: 378px;
                max-width: 410px;
            }
            #fitem_id_status{
                .custom-select{
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 9L12.333 15L18.333 9' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
                    background-repeat: no-repeat;     
                    background-size: 24px;  
                }
            }
            #fitem_id_department_id{
                .tree-selector-wrapper::after {
                    content: '';
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%) rotate(0deg); /* mũi tên xuống */
                    width: 24px;
                    height: 24px;
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 9L12.333 15L18.333 9' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
                    background-repeat: no-repeat;
                    background-size: contain;
                    pointer-events: none;
                }
                .tree-selector-wrapper[data-open="true"]::after {
                transform: translateY(-50%) rotate(180deg);
                }
            }
            input[type="checkbox"]{
                width: 18px;
                height: 18px;
            }
            #fgroup_id_estimated_cost{
                height: 76px !important;

                .inner-reverse{
                    max-height: 48px;
                    gap: 6px;
                }
                .mb-3.fitem:first-of-type{
                    flex: 1;
                }
                .estimated_cost_id{
                    flex: none !important;
                }
            }
            .text-paragraph{
                color:#44494D;
            }
            .form-check-input {
                margin: 0 !important;
            }
            #fgroup_id_buttonar{
                grid-row: 7;

                input#id_cancel{
                    width: 75px;
                    height: 40px;
                    padding: 8px 12px;
                    font-size: 16px !important;
                }
                input#id_submitbutton{
                    width: 84px;
                    height: 40px;
                    padding: 8px 12px;
                    font-size: 16px !important;

                }
            }
            .img-focus{
                grid-row: 4 !important;
            }
            .desc-focus{
                grid-row: 5 !important;
            }
        }
        .form-programs.view_form_programs{
            display: grid;
            grid-template-columns: repeat(3, minmax(auto, 410px));
            grid-template-rows: repeat(2, 60px);
            gap: 24px;
            position: relative;
            margin-top: 32px;

            .edw-form-label{
                font-size: 16px;
                color:#6F767E;
                font-weight: 400;
                line-height: 24px;
            }
            #fitem_id_departmentname{
                height: 60px;
            }
            .time.time_id{
                height: 60px;
                #fitem_id_starttime, 
                #fitem_id_endtime{
                    gap: 12px;
                    height: 60px !important;

                    .col-form-label{
                        max-height: 22px;
                        
                        .edw-form-label{
                            max-height: 22px;
                        }
                    }
                    .form-control{
                        max-height: 26px;
                    }
                }
            }
            #fgroup_id_estimated_cost{
                .felement{
                    .fitem{
                        span{
                            font-size: 16px;
                            color:#6F767E;
                            font-weight: 400;
                            line-height: 24px;
                        }
                    }
                }
                .estimated_cost_id{
                    margin: 0 !important;
                }
            }
            .certificatescertificates_id,
            .requiredrequired_id{
                .fitem{
                    gap: 0;
                }
                .text-paragraph{
                    font-size: 16px;
                    color:#120C0D;
                    font-weight: 500;
                    line-height: 24px;
                }
            }
            #fitem_id_status{
                .felement{
                    font-size: 16px;
                    color:#120C0D;
                    font-weight: 500;
                    line-height: 24px;
                }
            }
        }
    }
    .form-group .select2-container{
        width: 100% !important;
    }
    .categoryid_view{
        display:none;
    }
    #fgroup_id_estimated_cost {
        input.form-control{
            max-width: none;
            width:100% !important;
        }
        .inner-reverse{
            flex-wrap:nowrap !important;
        }
        #id_unit{
            width: 80px;
            padding: 8px;
        }

    }
    .select2-search__field{
        outline-color: #f03;
    }
    .placeholder-text .select2-selection__rendered{
        color: #B5B4B4;
    }
    .select2-selection__arrow{
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 9L12.333 15L18.333 9' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        padding-right: 26px;
    }
    
    .select2-selection__arrow b[role="presentation"]{
        display: none;
    }
    .select2-container--open .select2-selection__arrow{
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24' fill='none'%3E%3Cpath d='M6.33301 15L12.333 9L18.333 15' stroke='%236F767E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        padding-right: 26px;
    }
    .examination-custom-form .form-programs.view_form_programs .time {
        height: 76px;
    }

/* page-local-workplace-program_courses_exams */
#page-local-workplace-program_courses_exams{
    .main-content-full .table-users thead {
        padding-right: 0px;
    }

    .main-content-full .table-users tbody {
        display: table;
    }
    .main-content-full .table-users thead .col-action {
        background-color: #FAFAFA;
    }
    .main-content-full .table-users tbody .col-action {
        background-color: #fff;
    }
    .col-action{
        width: 88px !important;
        max-width: 88px !important;
    }
    .main-content-full .table-users .col-action.sticky-col {
        position: sticky;
        right: 0;
        z-index: 2;
        box-shadow: -2px 0 1px rgba(0, 0, 0, 0.05);
    }
    .main-content-full .table-users input[type="checkbox"]:not(.custom-control-input) {
        width: 18px;
        height: 18px;
    }
     .examination-title{     
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 65%;
        }
}
#page-local-workplace-program_courses_exams {
    .name-program-with {
        width: 242px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
    }
    @media (max-width: 428px){
        #clearfilter {
            display: none;
        }
        .filter-container .gap-search-custom {
            gap: 0;
        }
        .examination-title-action .examination-title~.gap-search {
            flex-wrap: wrap;
        }
        .examination-quiz-breadcrumb {
            flex-wrap: wrap;
        }
        .main-content-full .examination-title-action .examination-title {
            font-size: 16px;
            line-height: 24px;
        }
        .examination-title {
            font-size: 16px !important;
        }
        .main-content-full .examination-title-action .gap-search {
            gap: 8px;
        }
        .main-content-full .examination-title-action .gap-search button {
            line-height: 22px;
            white-space: nowrap;
        }
        .main-content-full .table-users {
            overflow-x: auto;
        }
        .main-content-full .table-users .custom-table {
            min-width: 1140px;
            max-width: 100%;
            overflow-x: auto;
            width: auto;
        }
        .btn.examination-btn-height-custom {
            padding: 15px 12px !important;
        }
    }
    @media (min-width: 429px) and (max-width: 833px) {
        #clearfilter {
            display: none;
        }
        .filter-container .gap-search-custom {
            gap: 0;
        }
        #page #topofscroll > div.container {
            padding: 0 24px !important;
        }
        .main-content-full .search-input {
            gap: 0px;
        }
        .examination-title-action {
            flex-direction: row !important;
            flex-wrap: wrap;
        }
        .examination-title {
            font-size: 18px !important;
        }
        .main-content-full .table-users {
            overflow-x: auto;
        }
        .main-content-full .table-users .custom-table {
            min-width: 1140px;
            max-width: 100%;
            overflow-x: auto;
            width: auto;
        }
    }
    @media (min-width: 834px) and (max-width: 1365px) {
        #page #topofscroll>div.container {
            max-width: unset;
        }
        /* .main-content-full {
            width: auto;
        } */
        /* .examination-title-action {
            flex-wrap: wrap;
        } */
        .btn.examination-btn-height-custom {
            white-space: nowrap;
        }
        .examination-title-action {
            gap:20px;
            flex-direction: row;
            align-items: center;
        }
        .main-content-full .examination-title-action .btn.examination-btn-height-custom {
            padding: 20px 12px !important;
        }
        .examination-title-action .examination-title~.gap-search {
            flex-wrap: nowrap;
        }
        .examination-title {
            font-size: 20px !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
        .btn.examination-btn-height-custom {
            font-size: 16px !important;
            padding: 20px 12px !important;
        }
        /* .main-content-full .table-users .custom-table thead th {
            white-space: nowrap;
            min-width: 165px;
            max-width: 165px;
        } */

        .name-program-with {
            width: 582px;
        }
        .main-content-full .table-users .custom-table .winput {
            width: 50px !important;
        }
        .main-content-full .table-users {
            overflow-x: auto;
        }
        .main-content-full .table-users .custom-table {
            min-width: 1500px;
            max-width: 100%;
            overflow-x: auto;
            width: auto;
        }
    }
}

.form-programs #fitem_id_name,
.form-programs > div:nth-child(2),
.form-programs #fitem_id_code,
.form-programs #fitem_id_department_id,
.form-programs #fgroup_id_estimated_cost,
.form-programs .time #fitem_id_starttime,
.form-programs .time #fitem_id_endtime,
.form-programs .edw-form-programs,
.form-programs #fitem_id_status,
.fgroup_id_estimated_cost {
    height: auto !important;
}

#fitem_id_image,
#fitem_id_description,
#fgroup_id_buttonar {
    grid-column: 1 / span 2;
}

.form-programs .form-label-addon {
    margin-left: 10px !important;
    flex: none !important;
}

.form-programs .edw-form-programs .edw-form-label.required, .form-programs .edw-form-label.certificatesw-form-label:not(.certificates):not(.required) {
    margin-right: 0 !important;
    flex: none !important;
}

@media (max-width: 834px) {
    .form-programs {
        grid-template-columns: 1fr;
    }
    .form-programs > * {
        max-width: 100%;
    }

    #fitem_id_image,
    #fitem_id_description,
    #fgroup_id_buttonar {
        grid-column: 1 / span 1;
    }

    .form-programs #fitem_id_name,
    .form-programs > div:nth-child(2),
    .form-programs #fitem_id_code,
    .form-programs #fitem_id_department_id,
    .form-programs #fgroup_id_estimated_cost,
    .form-programs .time #fitem_id_starttime,
    .form-programs .time #fitem_id_endtime,
    .form-programs .edw-form-label.required, .form-programs .edw-form-label.certificates,
    .form-programs #fitem_id_status,
    .fgroup_id_estimated_cost {
        max-width: 349px !important;
    }

    .form-programs .form-label-addon {
        margin-left: 10px !important;
        flex: none !important;
    }

    .form-programs .form-programs .edw-form-label.required, .form-programs .edw-form-label.certificates:not(.certificates):not(.required) {
        margin-right: 0 !important;
        flex: none !important;
    }

    .form-group:not(.hidden) {
        display: flex;
        gap: 12px;
        flex-direction: column;
        flex-wrap: nowrap;
    }
    
    input[name="is_issuance_certificates"], input[name="is_required"] {
        width: 16px !important;
        height: 16px !important;
    }
}

@media (max-width: 1200px) {
    #page #topofscroll > div.container {
        padding: 0 24px !important;
    }

    .form-programs #fitem_id_name,
    .form-programs > div:nth-child(2),
    .form-programs #fitem_id_code,
    .form-programs #fitem_id_department_id,
    .form-programs #fgroup_id_estimated_cost,
    .form-programs .time #fitem_id_starttime,
    .form-programs .time #fitem_id_endtime,
    .form-programs .edw-form-label.required, .form-programs .edw-form-label.certificates,
    .form-programs #fitem_id_status,
    .fgroup_id_estimated_cost {
        min-width: 0 !important;
        width: 100% !important;
    }

    .form-programs {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px 32px;
    }

    .form-programs {
        display: flex !important;
        flex-wrap: wrap !important;
    }

    .form-group .row .fitem {
        display: flex !important;
        flex-direction: column !important;
    }

    /* .form-programs #fitem_id_image, */
    .form-programs #fitem_id_description,
    .form-programs #fgroup_id_buttonar {
        max-width: 722px !important;
        width: 100% !important;
        min-width: 0 !important;
    }

    .form-programs #fgroup_id_buttonar {
        margin-top: -16px !important;
    }

    .form-group:not(:last-child) {
        margin-bottom: 0 !important;
    }

    #fitem_id_code { order: 1; }
    #fitem_id_name { order: 2; }
    #fitem_id_category_id { order: 3; }
    #fitem_id_department_id { order: 4; }
    .time { order: 5; }
    #fgroup_id_estimated_cost { order: 7; }
    .certificates { order: 8; }
    #fitem_id_status { order: 9; }
    .required { order: 10; }
    #fitem_id_image { order: 11; }
    #fitem_id_description { order: 12; }
    #fgroup_id_buttonar { order: 13; }
}



@media (max-width: 428px) {
    #fgroup_id_estimated_cost { order: 7; }
    .certificates { order: 8; }
    .required { order: 9; }
    #fitem_id_status { order: 10; }

    .form-programs {
        margin-top: 16px !important;
    }
    .form-programs #fitem_id_name,
    .form-programs > div:nth-child(2),
    .form-programs #fitem_id_code,
    .form-programs #fitem_id_department_id,
    .form-programs #fgroup_id_estimated_cost,
    .form-programs .time #fitem_id_starttime,
    .form-programs .time #fitem_id_endtime,
    .form-programs .edw-form-label.required, .form-programs .edw-form-label.certificates,
    .form-programs #fitem_id_status,
    .fgroup_id_estimated_cost {
        max-width: 396px !important;
        height: auto !important;
    }

    .form-programs #fitem_id_department_id {
        height: auto !important;
    }

    input[name="is_issuance_certificates"], input[name="is_required"] {
        width: 18px !important;
        height: 18px !important;
    }
}

label[for="id_is_issuance_certificates"], label[for="id_is_required"] {
    color: #44494D;
}

#page-local-workplace-edit_lecturers {
    .form-programs {
        #fitem_id_avatar {
            width: 100%;
            grid-column: 1/ span 3;
            grid-row: -4;
            margin: 0;
            max-width: none;
        }
    }
    
}
.page-link:focus {
    border-color: transparent !important;
    box-shadow: none;
    border: none;
}
.courses-time-dropdown-toggle:focus-within{
    border-color: transparent !important;
    box-shadow: 0 0 0 1px #f03 !important;
    border: 1px solid #f03 !important;
}
#page-local-workplace-edit_training_program_category .placeholder-text{
    color:#B7BABE !important;
}

td.col-priority, th.col-priority {
    width: 75px;
    text-align: center !important;
}

.examination-btn-reset:hover {
    background-color: #F03;
}
.examination-btn-reset:hover svg path {
    stroke: #FFF;
}
.error-validate {
    border: 1px solid #dc3545 !important;
}
.thead-sticky{
    position: sticky;
    top: 0;
    z-index: 10;
}

.custom_truncate{
    white-space: nowrap;      
    overflow: hidden;         
    text-overflow: ellipsis; 
}

#page-local-workplace-edit_programs .moodle-dialogue.filepicker{
    display: none !important;
}