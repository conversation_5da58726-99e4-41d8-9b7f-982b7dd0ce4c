<div class="d-flex">
    <!-- Main Content -->
    <div class="main-content-full program">
        <h2 class="main-title">{{#str}}training_program_list, local_workplace{{/str}}</h2>
        <!-- Filter Section -->
        <div class="filter-container p-0 border-0">
            <div class="filter-row">  
                <div class="column-name form-control" tabindex="0">
                    <span class="edw-icon edw-icon-Search child-focus">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 19L13 13M1 8C1 8.91925 1.18106 9.82951 1.53284 10.6788C1.88463 11.5281 2.40024 12.2997 3.05025 12.9497C3.70026 13.5998 4.47194 14.1154 5.32122 14.4672C6.1705 14.8189 7.08075 15 8 15C8.91925 15 9.82951 14.8189 10.6788 14.4672C11.5281 14.1154 12.2997 13.5998 12.9497 12.9497C13.5998 12.2997 14.1154 11.5281 14.4672 10.6788C14.8189 9.82951 15 8.91925 15 8C15 7.08075 14.8189 6.1705 14.4672 5.32122C14.1154 4.47194 13.5998 3.70026 12.9497 3.05025C12.2997 2.40024 11.5281 1.88463 10.6788 1.53284C9.82951 1.18106 8.91925 1 8 1C7.08075 1 6.1705 1.18106 5.32122 1.53284C4.47194 1.88463 3.70026 2.40024 3.05025 3.05025C2.40024 3.70026 1.88463 4.47194 1.53284 5.32122C1.18106 6.1705 1 7.08075 1 8Z"
                                stroke="#44494d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
                    <input type="text" name="keyword" class="programs-control child-focus" value="{{keyword}}" placeholder='{{#str}}search_by_training_program_name_and_code, local_workplace{{/str}}' />
                </div>
                <div class="block-filter">
                    <button class="action-buttons-filter-extend d-flex align-items-center examination-btn-height-custom action-button-filter-mobile" type="button" id="filterquestionbank">
                        <div class="img-div">
                            <img src="{{baseurl}}/local/examination/image/icon-filter.png" alt="Vector Image">
                        </div>
                        <span>
                            {{#str}} filter, local_examination {{/str}}
                        </span>
                    </button>
                </div>
                <div class="column-org examination-input-tree filter-hidden">
                    <input type="text" id="organizationid" name="organizationid" class="form-control custom-select" placeholder="{{organization_name}}">
                </div>
                <div class=" column-status filter-hidden">
                    <select id="status" name="status" class="form-control examination-custom-select">
                        <option value="-1">{{#str}}all, local_examination{{/str}}</option>
                        {{#status}}
                            <option value="{{id}}" {{#selected}}selected{{/selected}}>{{name}}</option>
                        {{/status}}
                    </select>
                </div>
                <div class="reset-column filter-hidden">
                    <button type="reset" name="clearfilter" class="form-control reset-button" >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.611 10.8645C16.4536 12.0644 15.9726 13.1989 15.2194 14.1463C14.4663 15.0936 13.4695 15.8181 12.3359 16.242C11.2024 16.6659 9.97478 16.7732 8.78485 16.5524C7.59491 16.3317 6.48752 15.7912 5.58143 14.9889C4.67533 14.1866 4.00473 13.1528 3.64153 11.9983C3.27832 10.8439 3.23623 9.61234 3.51976 8.43579C3.80329 7.25923 4.40175 6.18205 5.25094 5.31976C6.10014 4.45748 7.16805 3.84262 8.34013 3.54113C11.5893 2.7078 14.9526 4.3803 16.1943 7.49697M16.6668 3.33029V7.49695H12.5001" stroke="#FF466F" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="dropdown time filter-hidden">
                {{! starttime }}
                <div class="courses-time-dropdown-toggle" id="starttime"  tabindex="0">
                    <div class="date-range-text">
                        <label class="date-range-label child-focus">
                            {{#str}}starttime, theme_remui{{/str}}:
                        </label>
                        <input type="text" id="dateRangePickerStartTime" class="child-focus" placeholder="{{#str}}starttime, theme_remui{{/str}}"
                               readonly/>
                    </div>

                    <svg class="filter-icon child-focus" width="20" height="20" viewBox="0 0 20 20" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.332 2.5V5.83333M6.66536 2.5V5.83333M3.33203 9.16667H16.6654M3.33203 5.83333C3.33203 5.39131 3.50763 4.96738 3.82019 4.65482C4.13275 4.34226 4.55667 4.16667 4.9987 4.16667H14.9987C15.4407 4.16667 15.8646 4.34226 16.1772 4.65482C16.4898 4.96738 16.6654 5.39131 16.6654 5.83333V15.8333C16.6654 16.2754 16.4898 16.6993 16.1772 17.0118C15.8646 17.3244 15.4407 17.5 14.9987 17.5H4.9987C4.55667 17.5 4.13275 17.3244 3.82019 17.0118C3.50763 16.6993 3.33203 16.2754 3.33203 15.8333V5.83333Z"
                            stroke="#4E5566" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>

                {{! endtime }}

                <div class="courses-time-dropdown-toggle" id="endtime"  tabindex="0">
                    <div class="date-range-text">
                        <label class="date-range-label">
                            {{#str}}endtime, theme_remui{{/str}}:
                        </label>
                        <input type="text" id="dateRangePickerEndTime" placeholder="{{#str}}endtime, theme_remui{{/str}}" readonly/>
                    </div>

                    <svg class="filter-icon" width="20" height="20" viewBox="0 0 20 20" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.332 2.5V5.83333M6.66536 2.5V5.83333M3.33203 9.16667H16.6654M3.33203 5.83333C3.33203 5.39131 3.50763 4.96738 3.82019 4.65482C4.13275 4.34226 4.55667 4.16667 4.9987 4.16667H14.9987C15.4407 4.16667 15.8646 4.34226 16.1772 4.65482C16.4898 4.96738 16.6654 5.39131 16.6654 5.83333V15.8333C16.6654 16.2754 16.4898 16.6993 16.1772 17.0118C15.8646 17.3244 15.4407 17.5 14.9987 17.5H4.9987C4.55667 17.5 4.13275 17.3244 3.82019 17.0118C3.50763 16.6993 3.33203 16.2754 3.33203 15.8333V5.83333Z"
                            stroke="#4E5566" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                 <div class="reset-column2" style="display: none">
                    <button type="reset" name="clearfilter" class="form-control reset-button" >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.611 10.8645C16.4536 12.0644 15.9726 13.1989 15.2194 14.1463C14.4663 15.0936 13.4695 15.8181 12.3359 16.242C11.2024 16.6659 9.97478 16.7732 8.78485 16.5524C7.59491 16.3317 6.48752 15.7912 5.58143 14.9889C4.67533 14.1866 4.00473 13.1528 3.64153 11.9983C3.27832 10.8439 3.23623 9.61234 3.51976 8.43579C3.80329 7.25923 4.40175 6.18205 5.25094 5.31976C6.10014 4.45748 7.16805 3.84262 8.34013 3.54113C11.5893 2.7078 14.9526 4.3803 16.1943 7.49697M16.6668 3.33029V7.49695H12.5001" stroke="#FF466F" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>

            </div>
        </div>
        <!-- Exam List -->
            <!-- Left Main -->
        <div class="d-flex main-programs">
            <button id="sidebarTraining" style="display:none">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M13 5H21M13 9H18M13 15H21M13 19H18M3 5C3 4.73478 3.10536 4.48043 3.29289 4.29289C3.48043 4.10536 3.73478 4 4 4H8C8.26522 4 8.51957 4.10536 8.70711 4.29289C8.89464 4.48043 9 4.73478 9 5V9C9 9.26522 8.89464 9.51957 8.70711 9.70711C8.51957 9.89464 8.26522 10 8 10H4C3.73478 10 3.48043 9.89464 3.29289 9.70711C3.10536 9.51957 3 9.26522 3 9V5ZM3 15C3 14.7348 3.10536 14.4804 3.29289 14.2929C3.48043 14.1054 3.73478 14 4 14H8C8.26522 14 8.51957 14.1054 8.70711 14.2929C8.89464 14.4804 9 14.7348 9 15V19C9 19.2652 8.89464 19.5196 8.70711 19.7071C8.51957 19.8946 8.26522 20 8 20H4C3.73478 20 3.48043 19.8946 3.29289 19.7071C3.10536 19.5196 3 19.2652 3 19V15Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <div class="left-main">
                <div class="title-programs examination-cart-box-header">
                    <span>{{#str}}training_program_catalog, local_workplace{{/str}}</span>
                    <a href="/program-categories" class="text-decoration-none focus-tab-nav-link-periods" tabindex="0" role="button">
                        <span class="title-other-categories">{{#str}} training_program_catalog , local_workplace {{/str}}</span>
                        <img src="{{baseurl}}/local/examination/image/right_arrow.png" class="ms-1" alt="Vector Image">
                    </a>
                    <span style="display:none" class="close-left-main">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                            <path d="M14.0625 3.9375L3.9375 14.0625" stroke="#6F767E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14.0625 14.0625L3.9375 3.9375" stroke="#6F767E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="top-bar">
                    <span class="top-bar-color"></span>
                </div>
            {{> local_workplace/program/index_list_programs }}
        </div>
        <div class="right-main">
            <!-- Action Bar -->
            <div class="action-bar m-0 align-items-center">
                <div class="title-programs">
                    <span>{{#str}}list_programs, local_workplace{{/str}}</span>
                </div>
                <div class="action-buttons">
                    <button
                            data-url="/local/workplace/programs.php"
                            data-confirm-delete-multiple
                            data-src="/local/examination/image/error.png"
                            data-confirm-header="{{#str}}delete_programs_title, local_workplace{{/str}}"
                            data-confirm-message="{{#str}} delete_programs_message, local_workplace {{/str}}"
                            class="btn btn-secondary action-buttons-import d-none align-items-center examination-btn-height-custom">
                            <div class="img-div">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M3.99609 7H19.9965M9.99626 11V17M13.9964 11V17M4.99612 7L5.99615 19C5.99615 19.5304 6.20687 20.0391 6.58195 20.4142C6.95703 20.7893 7.46575 21 7.9962 21H15.9964C16.5269 21 17.0356 20.7893 17.4107 20.4142C17.7858 20.0391 17.9965 19.5304 17.9965 19L18.9965 7M8.99623 7V4C8.99623 3.73478 9.10159 3.48043 9.28913 3.29289C9.47667 3.10536 9.73103 3 9.99626 3H13.9964C14.2616 3 14.5159 3.10536 14.7035 3.29289C14.891 3.48043 14.9964 3.73478 14.9964 4V7" stroke="#FF1F4F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </div>
                        {{#str}} delete, local_examination {{/str}}
                    </button>
                    <a id="create-program-btn" href="/local/workplace/edit_programs.php" class="btn btn-primary action-create action-create-text examination-btn-height-custom" style="{{disabled_class}}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M11.9996 5V19M4.99939 12H18.9998" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        {{#str}} add, local_workplace{{/str}}
                    </a>
                </div>
            </div>
            {{#items.0}}
                <div class="custom-height">
                    <div class="responsive-table-wrapper">
                        <table class="table border custom-table mb-0">
                            <thead style="top:0 ;position:sticky; z-index:10">
                                <tr>
                                    <th class= "text-center checkbox-programs"><input type="checkbox" name="check_all" class="checkbox" id="select-all"></th>
                                    <th class= "text-center col-code">{{#str}} program_code, local_workplace {{/str}}</th>
                                    <th class= "text-center col-name">{{#str}} program_name, local_workplace {{/str}}</th>
                                    <th class= "text-center col-times">{{#str}} time_start, local_workplace {{/str}}</th>
                                    <th class= "text-center col-times">{{#str}} time_end, local_workplace {{/str}}</th>
                                    <th class= "text-center col-category">{{#str}} training_program_category_organization, local_workplace {{/str}}</th>
                                    <th class= "text-center col-required">{{#str}} required, local_workplace {{/str}}</th>
                                    <th class= "text-center col-number">{{#str}} course_number, local_workplace {{/str}}</th>
                                    <th class= "text-center col-number">{{#str}} student_number, local_workplace {{/str}}</th>
                                    <th class= "text-center col-status">{{#str}} status, local_workplace {{/str}}</th>
                                    <th class= "text-center col-action sticky-col">{{#str}} action, local_examination {{/str}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#items}}
                                    {{> local_workplace/program/index_programs }}
                                {{/items}}
                            </tbody>
                        </table>
                    </div>
                </div>
            {{/items.0}}
            {{^items}}          
                {{#hasquery}}
                    <div class="text-name ms-3">
                        {{#str}} no_result_found, local_workplace {{/str}}
                    </div>
                {{/hasquery}}
                {{^hasquery}}
                    <div class="text-name ms-3" style="height:530px">
                        <div class="wrapper">
                            <div class="empty-state program">
                                <img src="/local/workplace/pix/icon/nodata.svg" alt="Không có dữ liệu" class="svg-file-light">
                                <img src="/local/workplace/pix/icon/nodata_dark.svg" alt="Không có dữ liệu" style="display:none" class="svg-file-dark">
                                <span>{{#str}}no_data_found, local_workplace{{/str}}</span>
                            </div>
                        </div>
                    </div>
                {{/hasquery}}
            {{/items}}
            <!-- Pagination -->
            {{#pagination}}
                <div class="examination-controls examination-category-controls">
                    {{{pagination}}}
                </div>
            {{/pagination}}
        </div>
    </div>   
   
</div>
<link rel="stylesheet" href="/local/workplace/styles/daterangepicker.css">
<script src="/local/workplace/js/moment.min.js"></script>
<script src="/local/workplace/js/daterangepicker.min.js"></script>
<script src="/local/workplace/js/locale/vi.js"></script>


<script>
    $(document).ready(function () {
          const filterButton = document.getElementById('filterquestionbank');
    const hiddenSections = document.querySelectorAll('.filter-hidden');

    filterButton.addEventListener('click', function () {
        hiddenSections.forEach(section => {
            section.classList.toggle('show');
        });
    });
    
        let cancelLabel = "{{#str}}cancel, local_workplace{{/str}}";
        let applyLabel = "{{#str}}confirm, local_workplace{{/str}}";

        const inputStart = $('#starttime');
        const inputEnd = $('#endtime');
        const lang = '{{lang}}';
        moment.locale(lang);

        const commonOptions = {
            autoUpdateInput: false,
            linkedCalendars: false,
            locale: {
                format: 'DD/MM/YYYY',
                cancelLabel: cancelLabel,
                applyLabel: applyLabel
            }
        };

        $('input[name="keyword"]').on('keypress', function (e) {
            if (e.which === 13) {
                e.preventDefault();
                const keyword = $(this).val().trim();

                const url = new URL(window.location.href);
                if (keyword) {
                    url.searchParams.set('keyword', keyword);
                } else {
                    url.searchParams.delete('keyword');
                }
                window.location.href = url.toString();
            }
        });

        $('#status').on('change', function () {
            const selectedStatus = $(this).val();
            const url = new URL(window.location.href);
            if (selectedStatus !== '') {
                url.searchParams.set('status', selectedStatus);
            } else {
                url.searchParams.delete('status');
            }
            url.searchParams.delete('page_index');
            window.location.href = url.toString();
        });
        
        // Initialize date pickers
        inputStart.data('daterangepicker')?.remove();
        inputEnd.data('daterangepicker')?.remove();

        inputStart.daterangepicker(commonOptions).on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
            updateURLParams('startdate', picker.startDate, picker.endDate);
            $(this).closest('.dropdown').removeClass('open');
            $(this).closest('.dropdown').find('.date-range-label').show();
        });
        inputEnd.daterangepicker(commonOptions).on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
            updateURLParams('enddate', picker.startDate, picker.endDate);
            $(this).closest('.dropdown').removeClass('open');
            $(this).closest('.dropdown').find('.date-range-label').show();
        });

        // --- Thêm logic toggle dropdown daterangepicker ---
        // Track open state for each input
        let pickerStates = {
            start: false,
            end: false
        };

        inputStart.off('click.daterangepicker');
        inputEnd.off('click.daterangepicker');

        inputStart.on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            if (pickerStates.start) {
                inputStart.data('daterangepicker').hide();
            } else {
                inputStart.data('daterangepicker').show();
            }
        });

        inputEnd.on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            if (pickerStates.end) {
                inputEnd.data('daterangepicker').hide();
            } else {
                inputEnd.data('daterangepicker').show();
            }
        });

        inputStart.on('show.daterangepicker', function () {
            pickerStates.start = true;
        });
        inputStart.on('hide.daterangepicker', function () {
            pickerStates.start = false;
        });

        inputEnd.on('show.daterangepicker', function () {
            pickerStates.end = true;
        });
        inputEnd.on('hide.daterangepicker', function () {
            pickerStates.end = false;
        });

        $(document).on('click', '.courses-time-dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if ($(this).find('#dateRangePickerStartTime').length) {
                const picker = $('#dateRangePickerStartTime').data('daterangepicker');
                if (!picker) return;
                const endPicker = $('#dateRangePickerEndTime').data('daterangepicker');
                if (endPicker && pickerStates.end) {
                    endPicker.hide();
                    pickerStates.end = false;
                }
                if (pickerStates.start) {
                    picker.hide();
                    pickerStates.start = false;
                } else {
                    picker.show();
                    pickerStates.start = true;
                }
            }
            if ($(this).find('#dateRangePickerEndTime').length) {
                const picker = $('#dateRangePickerEndTime').data('daterangepicker');
                if (!picker) return;
                const startPicker = $('#dateRangePickerStartTime').data('daterangepicker');
                if (startPicker && pickerStates.start) {
                    startPicker.hide();
                    pickerStates.start = false;
                }
                if (pickerStates.end) {
                    picker.hide();
                    pickerStates.end = false;
                } else {
                    picker.show();
                    pickerStates.end = true;
                }
            }
        });
        // --- Kết thúc thêm logic toggle dropdown daterangepicker ---

        $('.cancel-btn').on('click', function () {
            const picker = input.data('daterangepicker');
            if (picker) {
                picker.setStartDate(moment());
                picker.setEndDate(moment());
                input.val('');
            }
            $(this).closest('.dropdown').removeClass('open');
        });

        setDateRangesFromURL();
    });

    function setDateRangesFromURL() {
        const url = new URL(window.location.href);
        const startFrom = url.searchParams.get('startdate_from');
        const startTo = url.searchParams.get('startdate_to');
        const endFrom = url.searchParams.get('enddate_from');
        const endTo = url.searchParams.get('enddate_to');

        const inputStart = $('#dateRangePickerStartTime');
        const inputEnd = $('#dateRangePickerEndTime');

        if (startFrom && startTo) {
            const start = moment(startFrom, 'YYYY-MM-DD');
            const end = moment(startTo, 'YYYY-MM-DD');
            inputStart.val(`${start.format('DD/MM/YYYY')} - ${end.format('DD/MM/YYYY')}`);
            inputStart.data('daterangepicker')?.setStartDate(start);
            inputStart.data('daterangepicker')?.setEndDate(end);
            inputStart.closest('.dropdown').find('.date-range-label').show();
        }

        if (endFrom && endTo) {
            const start = moment(endFrom, 'YYYY-MM-DD');
            const end = moment(endTo, 'YYYY-MM-DD');
            inputEnd.val(`${start.format('DD/MM/YYYY')} - ${end.format('DD/MM/YYYY')}`);
            inputEnd.data('daterangepicker')?.setStartDate(start);
            inputEnd.data('daterangepicker')?.setEndDate(end);
            inputEnd.closest('.dropdown').find('.date-range-label').show();
        }
    }


    function updateURLParams(paramPrefix, startDate, endDate) {
        const url = new URL(window.location.href);
        const params = url.searchParams;
        params.set(`${paramPrefix}_from`, startDate.format('YYYY-MM-DD'));
        params.set(`${paramPrefix}_to`, endDate.format('YYYY-MM-DD'));
        if (params.has('page_index')) {
            params.delete('page_index');
        }
        window.location.href = url.toString();
    }
    const btn = document.getElementById('sidebarTraining');
    const leftMain = document.querySelector('.left-main');
    const closeBtn = document.querySelector('.close-left-main');

    btn.addEventListener('click', () => {
    leftMain.classList.toggle('active');
    closeBtn.style.display = 'block'; // Hiện nút close khi mở sidebar
    });

    closeBtn.addEventListener('click', () => {
    leftMain.classList.remove('active');
    closeBtn.style.display = 'none'; // Ẩn nút close khi đóng sidebar
    });
</script>
{{#js}}
require(['jquery', 'local_examination/select2'], function($) {

    $('.column-name .child-focus').on('click', function(e) {
        var $parent = $(this).closest('.column-name');
        $parent.focus();
        $parent.find('input.programs-control').focus(); 
        e.preventDefault();
    });
    $('.courses-time-dropdown-toggle .child-focus').on('click', function(e) {
        var $parent = $(this).closest('.courses-time-dropdown-toggle');
        $parent.focus();
        e.preventDefault();
    });

    $(document).ready(function() {
        const urlParams = new URLSearchParams(window.location.search);
        const category = urlParams.get('category');
        if(category){
            const $link = $('#create-program-btn');
            const href = $link.attr('href');

            const newHref = href + (href.includes('?') ? '&' : '?') + 's_category_id=' + encodeURIComponent(category);

            $link.attr('href', newHref);
        }
    });

    function loadExaminationChildren(parentElement, parentId, triggerElement) {
        $.ajax({
            url: M.cfg.wwwroot + '/local/workplace/programs.php',
            type: 'GET',
            data: {
                ajax: 1,
                parent_id: parentId
            },
            success: function (response) {
                const children = JSON.parse(response);
                let childrenHtml = '<ul class="children child-expanse-list">';
                const urlParams = new URLSearchParams(window.location.search);
                const currentExaminationCategoryId = urlParams.get('category');

                children.forEach(function (child) {
                    const isSelected = (child.id == currentExaminationCategoryId) ? 'selected-category' : '';
                    const lineThrough = child.line_through ? 'line-through' : '';

        // Determine which icon to use based on lineThrough
        const iconFile = child.line_through
        ? '/local/workplace/pix/icon/file-inactive.png'
        : '/local/workplace/pix/icon/file.svg';
        const iconFileDark = child.line_through
        ? '/local/workplace/pix/icon/file-inactive.png'
        : '/local/workplace/pix/icon/file-darkmode.svg';

                    childrenHtml += `
                        <li class="item list-unstyled" data-parent_id="${child.parent}" data-id="${child.id}">
                            <div class="item-name ${isSelected}">
                                <div class="d-flex align-items-center w-100 h-100">
                                    ${child.has_children ? `
                                        <div class="show-children">
                                            <span class="icon fa fa-chevron-right fa-fw"></span>
                                            <span class="icon fa fa-chevron-down fa-fw"></span>
                                        </div>
                                        <img src="${iconFile}" alt="Vector Image" class="svg-file-light">
                                        <img src="${iconFileDark}" alt="Vector Image" style="display:none" class="svg-file-dark">
                                    ` : `
                                        <img src="${iconFile}" alt="Vector Image" class="svg-file-light">
                                    <img src="${iconFileDark}" alt="Vector Image" style="display:none" class="svg-file-dark">
                                    `}
                                    ${child.can_interact ? `
                                        <a href="/local/workplace/programs.php?category=${child.id}" class="nav-link-periods w-100 h-100">
                                            <span class="name title-text-periods text-name ${lineThrough}">${child.name}</span>
                                        </a>
                                    ` : `
                                        <span class="name title-text-periods text-name ${lineThrough}">${child.name}</span>
                                    `}
                                </div>
                            </div>
                        </li>
                    `;
                });

                childrenHtml += '</ul>';
                parentElement.append(childrenHtml);
                $(triggerElement).data('loaded', true);
                if (typeof callback === 'function') {
                    callback()
                }
            },
            error: function (error) {
                console.log(error.message);
            }
        });
    }

    // uncollapse
   $(document).on('click', '.show-children', function (e) {
        e.preventDefault();
        const trigger = $(this);
        const parentElement = trigger.closest('.item-name').closest('.item');
        const parentId = parentElement.data('id');

        trigger.toggleClass('expand');

        let opened = JSON.parse(localStorage.getItem('openedCategories') || '[]');

        if (trigger.data('loaded')) {
            parentElement.find('> .children').toggle();

            if (parentElement.find('> .children').is(':visible')) {
                if (!opened.includes(parentId)) opened.push(parentId);
            } else {
                opened = opened.filter(id => id !== parentId);
            }
            localStorage.setItem('openedCategories', JSON.stringify(opened));
            return;
        }

        loadExaminationChildren(parentElement, parentId, trigger);

        if (!opened.includes(parentId)) {
            opened.push(parentId);
            localStorage.setItem('openedCategories', JSON.stringify(opened));
        }
    });



    $(document).ready(function () {
    const opened = JSON.parse(localStorage.getItem('openedCategories') || '[]');

    opened.forEach(function (parentId) {
        const parentElement = $(`[data-id="${parentId}"]`).first();
        const trigger = parentElement.find('.show-children');

        if (!trigger.data('loaded')) {
            loadExaminationChildren(parentElement, parentId, trigger, function () {
                parentElement.find('> .children').show();
                trigger.addClass('expand');
            });
        } else {
            parentElement.find('> .children').show();
            trigger.addClass('expand');
        }
    });

    setTimeout(function () {
        const selected = $('.selected-category').first();
        const container = $('.question-categories-container');

        if (selected.length && container.length) {
                const selectedTop = selected.offset().top;
                const containerTop = container.offset().top;

                const scrollOffset = selectedTop - containerTop;
                const scrollCenter = scrollOffset - (container.height() / 2) + (selected.outerHeight() / 2);

                container.animate({
                    scrollTop: container.scrollTop() + scrollCenter
                }, 500);
                $('html, body').animate({
                        scrollTop: selected.offset().top - 100 
                }, 500);
            }
        }, 500);
    });


    // clear filter
    $(document).on('click', 'button[name="clearfilter"]', function(e) {
        e.preventDefault();
        const params = new URLSearchParams(window.location.search);
        const category = params.get('category');
        const currentUrl = window.location.pathname;

        if (category) {
            window.location.href = `${currentUrl}?category=${category}`;
        } else {
            window.location.href = currentUrl;
        }
    });

    // Initialize tree view for the organization selector
    $(document).ready(function () {
        new TreeSelector('#organizationid', {
            fetchUrl: M.cfg.wwwroot + '/local/examination/classes/util/custom_api.php?sesskey=' + M.cfg.sesskey + '&function=get_tree_orgs',
            placeholder: '{{#str}}unit, local_examination{{/str}}',
            initialValue: {
                {{#organizations}}{{#selected}}id:{{id}}, label:'{{name}}'{{/selected}}{{/organizations}}
            },
            parentParamName: 'parent',
            onSelect: (node) => {
                const url = new URL(window.location.href);
                if (node && node.id) {
                    url.searchParams.set('organizationid', node.id);
                } else {
                    url.searchParams.delete('organizationid');
                }
                window.location.href = url.toString();
            }
        });
    });

    // btn delete state
    function updateDeleteButtonState() {
        const checkedItems = $('[name="check_id"]:checked');
        const deleteButton = $('[data-confirm-delete-multiple]');
        const isDarkMode = $('body').hasClass('dark-mode');

        if (checkedItems.length === 0) {
            deleteButton.css({
                'pointer-events': 'none',
                'cursor': 'not-allowed',  

                 ...(isDarkMode && {
                        'background-color': '#fed7df26',
                         'color': '#ff1840',

                                    
                    })
            });
            return;
        }

        let hasInvalidStatus = false;
        checkedItems.each(function () {
            const status = parseInt($(this).data('status'));
            if (status === 1) {
                hasInvalidStatus = true;
                return false;
            }
        });

        if (hasInvalidStatus) {
             deleteButton.css({
                'pointer-events': 'none',
                'opacity': '0.5',
                'cursor': 'not-allowed'
            });
        } else {
            deleteButton.css({
                'pointer-events': '',
                'opacity': '',
                'cursor': ''
            });
        }
        const checkedBoxes = $('input[name="check_id"]:checked');
    }

    $(document).on('change', '[name="check_id"], [name="check_all"]', function () {
        updateDeleteButtonState();
    });
    updateDeleteButtonState();

    // show toast message
    require(['local_workplace/toast'], function(toast) {
        $(document).ready(function() {
            {{#toast}}
                toast.showToast('{{title}}', '{{message}}', '{{status}}');
            {{/toast}}
        });
    });
});
{{/js}}