[id*="local-workplace"] ::placeholder{
    color: #8C94A3;
    opacity: 1;
    filter: alpha(opacity=100);
}
[id*="local-workplace"] ::-webkit-input-placeholder{
    color: #8C94A3;
    opacity: 1;
    filter: alpha(opacity=100);
}
[id*="local-workplace"] :-moz-placeholder{
    color: #8C94A3;
    opacity: 1;
    filter: alpha(opacity=100);
}
[id*="local-workplace"] ::-moz-placeholder{
    color: #8C94A3;
    opacity: 1;
    filter: alpha(opacity=100);
}
[id*="local-workplace"] :-ms-input-placeholder{
    color: #8C94A3;
    opacity: 1;
    filter: alpha(opacity=100);
}
.form-autocomplete-selection{
    min-height: auto;
}
/* workplace-organization-list */
.workplace-organization-list ul{
    margin: 5px 0 10px;
}
.workplace-organization-list li{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    /*padding: 10px 20px;*/
    background-color: #fff;
}
.workplace-organization-list li:hover{
    /*background-color: #e9ecef;*/
    /*background-color: #f8f9fa;*/
    /*background-color: #fed7df;*/
    border-radius: 8px;
}
.workplace-organization-list > li:hover{
    /*box-shadow: 5px 0 #E03 inset;*/
}
.workplace-organization-list li:not(:first-child){
    border-top: none !important;
}
.workplace-organization-list .item-name{
    flex: 1 1 auto;
    /*gap: 16px;*/
    gap: 2px;
    width: 1%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    height: 44px;
    margin: 0;
    margin-inline-end: 2px;
    /*padding: 12px 16px;*/
}
.workplace-organization-list .item-name .icon{
    display: none;
    font-size: 18px;
    color: rgb(0, 0, 0);
    /*margin-inline-end: 10px;*/
    margin: 0 !important;
}
/*.workplace-organization-list .item-name .icon.fa-chevron-down {*/
/*    display: block;*/
/*}*/
.workplace-organization-list .show-children{
    cursor: pointer;
}
.workplace-organization-list .show-children:hover{
    background-color: #fed7df;
    border-radius: 8px;
}
.workplace-organization-list .show-children:not(.expand) .fa-plus{
    display: block;
}
.workplace-organization-list .show-children.expand .fa-minus{
    display: block;
}
.workplace-organization-list .show-children:not(.expand) .fa-chevron-right{
    display: block;
}
.workplace-organization-list .show-children.expand .fa-chevron-down{
    display: block;
}
.workplace-organization-list .item-name .name{
    flex: 1 1 auto;
    width: 1%;
    font-size: 0.875rem;
    line-height: inherit;
    margin: 0;
    font-weight: 500;
}
.workplace-organization-list .item-name p{
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
    line-height: 18px;
    color: #494B57;
    margin: 2px 0;
}
.workplace-organization-list .item-name p:not(:last-child):after{
    content: '-';
    margin: 0 5px;
}
.workplace-organization-list .item-name{}
.workplace-organization-list .item-name{}
.workplace-organization-list .item-name{}
.workplace-organization-list .children{
    width: 100%;
}
/* workplace-role_org_assignment-table */
table.workplace-role_org_assignment-table{
    box-shadow: none;
    margin-top: 20px;
}
table.workplace-role_org_assignment-table thead th,
table.workplace-role_org_assignment-table tbody td{
    padding: 10px;
    vertical-align: top;
}
.workplace-role_org_assignment-table .name .wrapper{
    display: flex;
    align-items: center;
}
.workplace-role_org_assignment-table .name .picture{
    width: 60px;
    height: 60px;
    border-radius: 90px;
    overflow: hidden;
    border: 1px solid #ced4da;
    margin: 3px 0;
    margin-inline-end: 12px;
}
.workplace-role_org_assignment-table .name .picture img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.workplace-role_org_assignment-table .name .info{
    flex: 1 1 auto;
    width: 1%;
}
.workplace-role_org_assignment-table .name .title{
    font-size: calc(100% + 2px);
    line-height: inherit;
    font-family: inherit;
    margin: 0;
}
.workplace-role_org_assignment-table .name .title span{
    font-size: calc(100% - 2px);
    opacity: .5;
    font-weight: 400;
}
.workplace-role_org_assignment-table .name .type{
    margin: 0;
}
.workplace-role_org_assignment-table .name .type span{
    opacity: .5;
}
.workplace-role_org_assignment-table .name a{
    display: inline-block;
    vertical-align: top;
    margin-top: 3px;
    text-decoration: none;
}
.workplace-role_org_assignment-table .action{
    white-space: nowrap;
    width: 1%;
    text-align: center;
}
.workplace-role_org_assignment-table .action .btn{
    border: none;
}
.workplace-role_org_assignment-table .role-org{
    color: #494B57;
}
.workplace-role_org_assignment-table .role-org .item:not(:last-child){
    margin-bottom: 5px;
}
.workplace-role_org_assignment-table .role-org .item h3{
    display: inline-block;
    vertical-align: top;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    margin-inline-end: 3px;
}
.workplace-role_org_assignment-table .role-org .item p{
    display: inline;
}
.workplace-role_org_assignment-table .role-org .item p span:not(:last-child):after{
    content: ',';
}
.workplace-role_org_assignment-table .action a{
    display: block;
    text-decoration: none;
    padding: 5px 0;
}
/* page-local-workplace-index */
#page-local-workplace-index .breadcrumb .breadcrumb-item:first-child,
#page-local-workplace-index .breadcrumb .breadcrumb-item:first-child + .breadcrumb-item:before{
    display: none;
}
/* page-local-workplace-edit */
#page-local-workplace-edit .form-check label:hover{
    color: #e03;
}
#page-local-workplace-edit .form-check label{
    display: inline !important;
    margin: 0 !important;
    cursor: pointer;
}
#page-local-workplace-edit .form-check label span{
    color: #8C94A3;
}
#page-local-workplace-edit .form-check input:checked + label:not(:hover){
    color: #17a2b8;
}
#page-local-workplace-edit .form-check input:checked + label:before{
    color: #fff;
    background-color: #17a2b8;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABXSURBVHgB7ZGxDQAgCAQZxf2XcpRXChNjEE34goJLKCi4JyBSFGkA0GZ1LWGzyUEPOOXaC4uc8vUobyC0+WswfBZPANbNLRFN7oTw5JcQrtwI4cuL4osBw24WOH/2jkQAAAAASUVORK5CYII=);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    border-color: transparent;
}
#page-local-workplace-edit .form-edit-info-item:not(:last-child),
#page-local-workplace-edit .form-edit-info,
#page-local-workplace-edit .form-edit-username{
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}
#page-local-workplace-edit .form-group .col-form-label .edw-form-label{
    font-size: inherit;
    padding: 13px 0;
}
#page-local-workplace-edit .form-group input.form-control,
#page-local-workplace-edit .form-group select.form-control{
    width: 100%;
    min-width: 300px !important;
}
#page-local-workplace-edit .form-group select.form-control option{
    cursor: pointer;
}
#page-local-workplace-edit .form-group select.form-control option:hover{
    background-color: light-dark(rgb(206, 206, 206), rgb(84, 84, 84));
}
#page-local-workplace-edit .form-autocomplete-selection{
    margin-bottom: 10px;
}
#page-local-workplace-edit .form-edit-username{
    align-items: center;
}
#page-local-workplace-edit .form-edit-username .user{
    font-size: 30px;
}
#page-local-workplace-edit .form-edit-username .user span{
    opacity: .5;
    font-size: 20px;
    font-weight: 400;
}
#page-local-workplace-edit .form-edit-info-item{
    display: flex;
    flex-wrap: wrap;
}
#page-local-workplace-edit .form-edit-info-item .form-edit-info-title{
    width: 25%;
    padding-inline-end: 10px;
}
#page-local-workplace-edit .form-edit-info-item .form-edit-info-title h4{
    font-size: inherit;
    font-weight: inherit;
}
#page-local-workplace-edit .form-edit-info-item .form-edit-info-fields{
    flex: 1 1 auto;
    width: 1%;
}

#page-local-workplace-edit .form-edit-info-item .fitem .col-form-label{
    flex: 0 0 auto;
    max-width: 100%;
    margin-bottom: 5px;
}
#page-local-workplace-edit .form-edit-info-item .fitem .felement{
    flex: 0 0 auto;
    max-width: 100%;
}
#page-local-workplace-edit .form-edit-info-item .fitem:not(:first-child){
    margin-top: 15px;
}
#page-local-workplace-edit .form-edit-info-item .fitem:not(.fitem-org-list) .col-form-label{
    display: none;
}
#page-local-workplace-edit .form-edit-info-item .fitem:not(.fitem-org-list) .checkbox{
    flex: 0 0 auto;
    max-width: 100%;
}
#page-local-workplace-edit .col-lg-3.col-md-4.col-form-label.p-0{
    margin: 0;
}
#page-local-workplace-edit .d-inline-block.mt-2.mr-1 {
    margin: 0 !important;
}
/*#page-local-workplace-edit .felement.p-0 {*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*}*/
.form-edit-info-title {
    margin-top: 3px;
}
#page-local-workplace-edit .form-edit-info-item .form-group{
    margin: 0;
}
#page-local-workplace-edit .felement > .form-edit-info-item.default{
    display: none;
}
#page-local-workplace-edit .hidden{
    display: none !important;
}
#page-local-workplace-edit .form-autocomplete-selection + .d-md-inline-block{
    margin: 0 !important;
}
#page-local-workplace-edit  .form-autocomplete-suggestions li{
    font-size: inherit;
    line-height: inherit;
    padding: 10px 15px;
}
#page-local-workplace-edit  .form-autocomplete-suggestions li .form-org-selector-suggestion{
    display: inline-block;
    vertical-align: top;
}
/* workplace-controls */
.workplace-controls{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--controls-gap);
    font-size: var(--controls-fz);
    line-height: var(--controls-lh);
    margin: 24px 0 0;
    --controls-gap: 20px;
    --controls-fz: 14px;
    --controls-lh: 20px;
    --input-pd: 7px;
    --input-pd-hz: 10px;
    --input-btn-hz: 16px;
}
.workplace-controls form{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: var(--controls-gap);
}
.workplace-controls form .col-form-label{
    font-size: inherit;
    line-height: inherit;
    color: inherit;
}
.workplace-controls form label{
    font-size: inherit;
    line-height: inherit;
    padding: calc(var(--input-pd) + 1px) 0;
    margin: 0 !important;
    color: #1D2026;
    font-weight: 400;
}
.workplace-controls .custom-select,
.workplace-controls .form-control{
    width: auto;
    height: auto;
    font-size: var(--controls-fz);
    line-height: var(--controls-lh);
    color: inherit;
    padding: var(--input-pd) var(--input-pd-hz);
}
.workplace-controls select.custom-select,
.workplace-controls select.form-control{
    padding-inline-end: calc(var(--input-pd-hz) + 15px);
    background-position: right var(--input-pd-hz) center;
    cursor: pointer;
}
.workplace-controls .control-form{
    display: flex;
    flex-wrap: wrap;
    /*gap: var(--controls-gap);*/
    gap: 16px;
}
.workplace-controls .control-form .fitem{
    display: flex;
    flex: 1 1 auto;
    width: 1%;
    margin: 0;
}
.workplace-controls .control-form .fitem .col-form-label{
    flex: 0 0 auto;
    width: auto;
    max-width: none;
}
.workplace-controls .control-form .fitem .felement{
    flex: 1 1 auto;
    width: 1%;
    max-width: none;
    display: flex !important;
    align-items: stretch !important;
    flex-direction: column !important;
}
.workplace-controls .control-form .fitem .form-control{
    width: 100%;
}
.workplace-controls .control-form .fitem [data-fieldtype="autocomplete"] .custom-select{
    height: 0;
    padding: 0;
    margin: 0;
}
.workplace-controls .control-form .fitem .form-autocomplete-selection{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    order: 1;
    gap: 5px;
    line-height: 22px;
    margin: 0;
}
.workplace-controls .control-form .fitem .form-autocomplete-selection > *{
    margin: 0 !important;
    box-shadow: none !important;
}
.workplace-controls .control-form .fitem .form-autocomplete-selection > *:not(.badge){
    display: none !important;
}
.workplace-controls .control-form .fitem .form-autocomplete-selection > *:first-child{
    margin-top: 10px !important;
}
.workplace-controls .control-form .fitem .form-autocomplete-selection + .d-md-inline-block{
    margin: 0 !important;
}
.workplace-controls .control-form .form-autocomplete-suggestions{
    min-width: 100%;
    margin-top: -1px !important;
    border-radius: 8px;
}
.workplace-controls .control-form .form-autocomplete-suggestions li{
    font-size: inherit;
    line-height: inherit;
    padding: var(--input-pd) var(--input-pd-hz);
}
.workplace-controls .control-form .form-autocomplete-suggestions li .form-org-selector-suggestion{
    display: inline-block;
    vertical-align: top;
}
.workplace-controls .control-form .form-autocomplete-suggestions li[aria-selected=true]{
    box-shadow: 0 0 0 .1rem rgb(0 81 249 / .25) inset;
    border-radius: inherit;
}
.workplace-controls .control-form .fitem > .form-control{
    flex: 1 1 auto;
    width: 100%;
}
.workplace-controls .control-form .fitem .felement .loading-icon{
    position: absolute;
    top: 0;
    right: var(--input-pd-hz);
    width: 36px;
    height: calc(var(--controls-lh) + var(--input-pd) * 2);
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}
.workplace-controls .control-form .control-form-actions{
    /*width: 100%;*/
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.workplace-controls .control-form .control-form-actions .fitem{
    flex: 0 0 auto;
    width: auto;
    display: block !important;
    padding: 0 !important;
}
.workplace-controls .control-form .control-form-actions .col-form-label{
    display: none;
}
.workplace-controls .control-form .control-form-actions .felement{
    width: 100%;
    max-width: 100%;
}
.workplace-controls .form-label-checkbox{
    cursor: pointer;
}
.workplace-controls .form-label-checkbox:hover{
    color: #e03;
}
.workplace-controls .form-label-checkbox input[type="checkbox"]{
    vertical-align: middle;
    margin: -4px 0 !important;
}
.workplace-controls .control-form .control-search{
    flex: 1 1 auto;
    flex-direction: column;
    width: 1%;
    /*gap: 10px;*/
    gap: 6px;
}
.workplace-controls .control-form .search-form-actions{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 16px;
}
.workplace-controls .control-form .search-form-actions #fitem_id_submitbutton{
    flex: 0 0 auto;
    width: auto;
    padding: 0 !important;
    margin: 0 !important;
    background-color: transparent;
}
.workplace-controls .btn{
    font-size: inherit;
    line-height: inherit;
    /*padding: var(--input-pd) var(--input-btn-hz);*/
    padding: 8px 12px;
}
.workplace-controls .search-form{
    width: 100%;
    background-color: #F5F7FA;
    /*padding: var(--controls-gap);*/
    border-radius: 16px;
    padding: 16px 24px;
}
.workplace-controls .search-form .search-form-actions{
    width: 100%;
    justify-content: flex-end;
}
.workplace-controls .search-form .search-form-actions{
    width: 100%;
    justify-content: flex-end;
}
.workplace-controls .control-perpage{
    gap: 6px;
}
.workplace-controls .select-form .fitem{
    flex: 0 0 auto;
    width: auto;
}
.workplace-controls .select-form{
    width: 100%;
}
.workplace-controls .pagination{
    flex: 1 1 auto;
    justify-content: flex-end;
}
.workplace-controls .pagination ul{
    margin: 0;
}
.workplace-controls .pagination .page-link{
    line-height: inherit;
    padding: var(--input-pd) 8px;
    min-width: calc(var(--input-pd) * 2 + var(--controls-lh) + 2px);
    text-align: center;
}
.workplace-controls .pagination .page-link span{
    display: inline-block;
    vertical-align: top;
    line-height: inherit;
    font-size: calc(100% + 6px);
    margin: 0 -5px;
}
.workplace-controls .result-count{
    padding: calc(var(--input-pd) + 1px) 0;
    margin: 0;
}
.workplace-controls .result-count span{
    text-decoration: underline;
}
.workplace-role_org_assignment-table + .workplace-controls{
    margin-bottom: 0;
}
/* organization-edit-form */
.organization-edit-form .readonly-date a[id*="_calendar"]{
    display: none;
}
.organization-edit-form .readonly-date select,
.organization-edit-form .custom-select:disabled{
    color: #4c5a73;
    opacity: 1;
    background-color: #e9ecef;
    border-color: #E0E0E0;
}
/*----- CATEGORIES -----*/
.local-categories-heading{
    margin: 0 0 30px;
}
.local-categories-wrap{}
.local-categories-content{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 30px;
}
.local-categories-content .local-categories-col{
    flex: 1 1 auto;
    width: 1%;
    background-color: #fff;
    border: 1px solid rgb(0 0 0 / .125);
    border-radius: .5rem;
}
.local-categories-content .local-categories-col-title{
    font-size: 20px;
    padding: 10px 15px;
    border-bottom: 1px solid rgb(0 0 0 / .125);
    background-color: rgb(0 0 0 / .03);
    margin: 0;
}
.local-categories-content .local-categories-col-content{
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    padding: 24px;
}
.local-categories-content .local-categories-col-start{
    flex: 1 1 auto;
}
.local-categories-content .local-categories-col-end{
    flex: 1 1 auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: center;
}
.local-categories-content .workplace-organization-list{
    width: 100%;
    padding: 0 !important;
}
.local-categories-content .control-perpage{
    color: #e03;
}
.local-categories-content .control-perpage label{
    display: inline-block;
    vertical-align: top;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
}
.local-categories-content .control-perpage select{
    display: inline-block;
    vertical-align: top;
    font-size: inherit;
    line-height: inherit;
    border: none;
    padding: 0;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAGCAYAAAD68A/GAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABnSURBVHgBdY2BDYAgDAS/OIAmbIIMxgaUCVzMuIjRFSoVYgjqJ22a/8uXdrhggNFiS/hQzWF0EYgPuNhD6g2gJWNTNTyfmKWF9S6eZ9xlT+CZIFEgNSAWULJYGe9Xpblt+pUCeULvX/CZJ2vLBw72AAAAAElFTkSuQmCC);
    background-size: auto;
    padding-inline-end: 10px;
    background-position: right;
    color: inherit;
    height: auto;
    cursor: pointer;
}
.local-categories-content .item .name{
    color: #e03;
    font-weight: inherit;
}
.local-categories-content .item .btn-link:not(:hover){
    color: #CED1D9 !important;
}
.local-categories-content nav.pagination{
    width: 100%;
}
/* prorgams */
.main-programs{
    gap:32px;
}
.left-main {
    width: 340px;
    height: 100%;
    max-height: 530px;
}
.left-main .card{
    max-height: 100% !important;
}
.left-main .question-categories-container{
    /* max-height: 530px !important; */
}
.right-main {
    width: 860px;
    height: 100%;
    max-height: 530px;
    overflow-x: auto;
}
.right-main .custom-table{
    width: auto !important;
    max-width: 100%;
    min-width: 1060px;
}
.right-main .custom-table thead{
    padding-right: 0;
}
.right-main .custom-table tbody{
    display: table !important;
    max-height: 100%;
}

.right-main .custom-table thead .col-action{
    background-color: #FAFAFA;
}
.right-main .custom-table tbody .col-action{
    background-color: #fff;
}
.right-main .custom-table tbody .col-action .custom-dropdown-wrapper{
    margin-left: 11px;
}
.right-main .text-center{
    font-weight: 400 !important;
}
.right-main td.text-center.col-code{
    font-weight: 500 !important;
}
.col-code {
    width: 120px;
    max-width: 120px;
}
td.col-code{
    padding: 0 !important;
}
.col-name{
    width: 300px;
    max-width: 300px;
    white-space: nowrap;      
    overflow: hidden;           
    text-overflow: ellipsis; 

}
td.col-name{
    padding: 0 !important;
}
.col-times{
    width: 160px;
    max-width: 160px;
}
td.col-times{
    padding: 0 !important;
}
.col-category{
    width: 140px;
    max-width: 140px;
}
td.col-category{
    padding: 0 !important;
}
.col-required{
    width: 140px !important;
    max-width: 140px;   
}
td.col-required{
    padding: 0 !important;
}
.col-number{
    width: 160px;
    max-width: 160px;
}
td.col-number{
    padding: 0 !important;
}
.col-status{
    width: 160px;
    max-width: 160px;
}
td.col-status{
    padding: 0 !important;
}
.col-action{
    width: 88px !important;
    max-width: 88px !important; 
}
.col-action.sticky-col{
  position: sticky;
  right: 0; 
  z-index: 2; 
  box-shadow: -2px 0 1px rgba(0, 0, 0, 0.05);
}
#page-local-workplace-programs .custom-pagination-container{
    padding: 0;
}
#page-local-workplace-programs .custom-dropdown-wrapper .examination-btn-action,
#page-local-workplace-courses_setup .custom-dropdown-wrapper .examination-btn-action,
#page-local-workplace-categories .custom-dropdown-wrapper .examination-btn-action
{
    opacity:unset;
    transition: none;
    display: flex;
    align-items: center;
    justify-content: center;
}
.a-link-programs{
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
}
.a-link-programs:hover{
    color: unset !important;
}
#page-local-workplace-programs .text-content-table-exam-shift{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
}
/* .a-link-programs{
    display: flex;
    align-items: center;
    height: 52px;
} */
/* #page-local-workplace-programs .custom-dropdown-toggle{
    background-color: unset;
    border-color: unset;
    color: #6F767E;
} */