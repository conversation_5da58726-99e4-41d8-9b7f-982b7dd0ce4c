define("core_reportbuilder/schedules",["exports","core/event_dispatcher","core/inplace_editable","core/notification","core/pending","core/prefetch","core/str","core/toast","core_reportbuilder/local/events","core_reportbuilder/local/selectors","core_reportbuilder/local/repository/modals","core_reportbuilder/local/repository/schedules"],(function(_exports,_event_dispatcher,_inplace_editable,_notification,_pending,_prefetch,_str,_toast,reportEvents,reportSelectors,_modals,_schedules){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors);let initialized=!1;_exports.init=reportId=>{(0,_prefetch.prefetchStrings)("core_reportbuilder",["deleteschedule","deletescheduleconfirm","disableschedule","editscheduledetails","enableschedule","newschedule","schedulecreated","scheduledeleted","schedulesent","scheduleupdated","sendschedule","sendscheduleconfirm"]),(0,_prefetch.prefetchStrings)("core",["confirm","delete"]),initialized||(document.addEventListener("click",(event=>{if(event.target.closest(reportSelectors.actions.scheduleCreate)){event.preventDefault();const scheduleModal=(0,_modals.createScheduleModal)(event.target,(0,_str.getString)("newschedule","core_reportbuilder"),reportId);scheduleModal.addEventListener(scheduleModal.events.FORM_SUBMITTED,(()=>{(0,_str.getString)("schedulecreated","core_reportbuilder").then(_toast.add).then((()=>{const reportElement=document.querySelector(reportSelectors.regions.report);(0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement)})).catch(_notification.default.exception)})),scheduleModal.show()}const scheduleToggle=event.target.closest(reportSelectors.actions.scheduleToggle);if(scheduleToggle){const pendingPromise=new _pending.default("core_reportbuilder/schedules:toggle"),scheduleStateToggle=+!Number(scheduleToggle.dataset.state);(0,_schedules.toggleSchedule)(reportId,scheduleToggle.dataset.id,scheduleStateToggle).then((()=>{scheduleToggle.closest("tr").classList.toggle("text-muted"),scheduleToggle.dataset.state=scheduleStateToggle;const stringKey=scheduleStateToggle?"disableschedule":"enableschedule";return(0,_str.getString)(stringKey,"core_reportbuilder")})).then((toggleLabel=>(scheduleToggle.parentElement.querySelector('label[for="'.concat(scheduleToggle.id,'"] > span')).innerHTML=toggleLabel,pendingPromise.resolve()))).catch(_notification.default.exception)}const scheduleEdit=event.target.closest(reportSelectors.actions.scheduleEdit);if(scheduleEdit){event.preventDefault();const triggerElement=scheduleEdit.closest(".dropdown").querySelector(".dropdown-toggle"),scheduleModal=(0,_modals.createScheduleModal)(triggerElement,(0,_str.getString)("editscheduledetails","core_reportbuilder"),reportId,scheduleEdit.dataset.scheduleId);scheduleModal.addEventListener(scheduleModal.events.FORM_SUBMITTED,(()=>{(0,_str.getString)("scheduleupdated","core_reportbuilder").then(_toast.add).then((()=>{const reportElement=scheduleEdit.closest(reportSelectors.regions.report);(0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement)})).catch(_notification.default.exception)})),scheduleModal.show()}const scheduleSend=event.target.closest(reportSelectors.actions.scheduleSend);if(scheduleSend){event.preventDefault();const triggerElement=scheduleSend.closest(".dropdown").querySelector(".dropdown-toggle");_notification.default.saveCancelPromise((0,_str.getString)("sendschedule","core_reportbuilder"),(0,_str.getString)("sendscheduleconfirm","core_reportbuilder",scheduleSend.dataset.scheduleName),(0,_str.getString)("confirm","core"),{triggerElement:triggerElement}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/schedules:send");return(0,_schedules.sendSchedule)(reportId,scheduleSend.dataset.scheduleId).then((0,_toast.add)((0,_str.getString)("schedulesent","core_reportbuilder"))).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)})).catch((()=>{}))}const scheduleDelete=event.target.closest(reportSelectors.actions.scheduleDelete);if(scheduleDelete){event.preventDefault();const triggerElement=scheduleDelete.closest(".dropdown").querySelector(".dropdown-toggle");_notification.default.saveCancelPromise((0,_str.getString)("deleteschedule","core_reportbuilder"),(0,_str.getString)("deletescheduleconfirm","core_reportbuilder",scheduleDelete.dataset.scheduleName),(0,_str.getString)("delete","core"),{triggerElement:triggerElement}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/schedules:delete");return(0,_schedules.deleteSchedule)(reportId,scheduleDelete.dataset.scheduleId).then((0,_toast.add)((0,_str.getString)("scheduledeleted","core_reportbuilder"))).then((()=>{const reportElement=scheduleDelete.closest(reportSelectors.regions.report);return(0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement),pendingPromise.resolve()})).catch(_notification.default.exception)})).catch((()=>{}))}})),initialized=!0)}}));

//# sourceMappingURL=schedules.min.js.map