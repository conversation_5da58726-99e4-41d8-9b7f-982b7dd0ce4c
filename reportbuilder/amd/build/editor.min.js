define("core_reportbuilder/editor",["exports","jquery","core/inplace_editable","core/loadingicon","core/notification","core/pending","core/templates","core/str","core/toast","core_reportbuilder/local/selectors","core_reportbuilder/local/editor/columns","core_reportbuilder/local/editor/conditions","core_reportbuilder/local/editor/filters","core_reportbuilder/local/editor/sorting","core_reportbuilder/local/editor/card_view","core_reportbuilder/local/repository/reports","core_reportbuilder/local/repository/modals"],(function(_exports,_jquery,_inplace_editable,_loadingicon,_notification,_pending,_templates,_str,_toast,reportSelectors,_columns,_conditions,_filters,_sorting,_card_view,_reports,_modals){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),_templates=_interopRequireDefault(_templates),reportSelectors=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(reportSelectors);let initialized=!1;_exports.init=()=>{(0,_columns.init)(initialized),(0,_conditions.init)(initialized),(0,_filters.init)(initialized),(0,_sorting.init)(initialized),(0,_card_view.init)(initialized),initialized||(document.addEventListener("click",(event=>{const toggleEditViewMode=event.target.closest(reportSelectors.actions.toggleEditPreview);if(toggleEditViewMode){event.preventDefault();const reportElement=event.target.closest(reportSelectors.regions.report),pendingPromise=new _pending.default("core_reportbuilder/reports:get"),toggledEditMode="1"!==toggleEditViewMode.dataset.editMode;(0,_loadingicon.addIconToContainer)(toggleEditViewMode).then((()=>(0,_reports.getReport)(reportElement.dataset.reportId,toggledEditMode))).then((response=>Promise.all([_jquery.default.parseHTML(response.javascript,null,!0).map((node=>node.innerHTML)).join("\n"),_templates.default.renderForPromise("core_reportbuilder/local/dynamictabs/editor",response)]))).then((_ref=>{let[responseJs,{html:html,js:js}]=_ref;return _templates.default.replaceNode(reportElement,html,js+responseJs)})).then((()=>pendingPromise.resolve())).catch(_notification.default.exception)}const reportEdit=event.target.closest(reportSelectors.actions.reportEdit);if(reportEdit){event.preventDefault();const reportModal=(0,_modals.createReportModal)(event.target,(0,_str.getString)("editreportdetails","core_reportbuilder"),reportEdit.dataset.reportId);reportModal.addEventListener(reportModal.events.FORM_SUBMITTED,(()=>{(0,_str.getString)("reportupdated","core_reportbuilder").then(_toast.add).then((()=>window.location.reload())).catch(_notification.default.exception)})),reportModal.show()}})),initialized=!0)}}));

//# sourceMappingURL=editor.min.js.map