{"version": 3, "file": "filters.min.js", "sources": ["../src/filters.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder filter management\n *\n * @module      core_reportbuilder/filters\n * @copyright   2021 Paul <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport {loadFragment} from 'core/fragment';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {getString} from 'core/str';\nimport Templates from 'core/templates';\nimport {add as addToast} from 'core/toast';\nimport DynamicForm from 'core_form/dynamicform';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {resetFilters} from 'core_reportbuilder/local/repository/filters';\n\n/**\n * Update filter button text to indicate applied filter count\n *\n * @param {Element} reportElement\n * @param {Number} filterCount\n */\nconst setFilterButtonCount = async(reportElement, filterCount) => {\n    const filterButtonLabel = reportElement.querySelector(reportSelectors.regions.filterButtonLabel);\n\n    if (filterCount > 0) {\n        filterButtonLabel.textContent = await getString('filtersappliedx', 'core_reportbuilder', filterCount);\n    } else {\n        filterButtonLabel.textContent = await getString('filters', 'moodle');\n    }\n};\n\n/**\n * Initialise module for given report\n *\n * @method\n * @param {Number} reportId\n * @param {Number} contextId\n */\nexport const init = (reportId, contextId) => {\n    const reportElement = document.querySelector(reportSelectors.forReport(reportId));\n    const filterFormContainer = reportElement.querySelector(reportSelectors.regions.filtersForm);\n\n    // Ensure we only add our listeners once (can be called multiple times by mustache template).\n    if (filterFormContainer.dataset.initialized) {\n        return;\n    }\n    filterFormContainer.dataset.initialized = true;\n\n    const filterForm = new DynamicForm(filterFormContainer, '\\\\core_reportbuilder\\\\form\\\\filter');\n\n    // Submit report filters.\n    filterForm.addEventListener(filterForm.events.FORM_SUBMITTED, event => {\n        event.preventDefault();\n\n        // After the form has been submitted, we should trigger report table reload.\n        dispatchEvent(reportEvents.tableReload, {}, reportElement);\n        setFilterButtonCount(reportElement, event.detail);\n\n        getString('filtersapplied', 'core_reportbuilder')\n            .then(addToast)\n            .catch(Notification.exception);\n    });\n\n    // Reset report filters.\n    filterForm.addEventListener(filterForm.events.NOSUBMIT_BUTTON_PRESSED, event => {\n        event.preventDefault();\n\n        const pendingPromise = new Pending('core_reportbuilder/filters:reset');\n        const reportParameters = reportElement.dataset.parameter;\n\n        resetFilters(reportId, reportParameters)\n            .then(() => getString('filtersreset', 'core_reportbuilder'))\n            .then(addToast)\n            .then(() => loadFragment('core_reportbuilder', 'filters_form', contextId, {\n                reportid: reportId,\n                parameters: reportParameters,\n            }))\n            .then((html, js) => {\n                Templates.replaceNodeContents(filterFormContainer, html, js);\n\n                dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                setFilterButtonCount(reportElement, 0);\n\n                return pendingPromise.resolve();\n            })\n            .catch(Notification.exception);\n    });\n\n    // Modify \"region-main\" overflow for big filter forms.\n    document.querySelector('#region-main').style.overflowX = \"visible\";\n};\n"], "names": ["setFilterButtonCount", "async", "reportElement", "filterCount", "filterButtonLabel", "querySelector", "reportSelectors", "regions", "textContent", "reportId", "contextId", "document", "forReport", "filterFormContainer", "filtersForm", "dataset", "initialized", "filterForm", "DynamicForm", "addEventListener", "events", "FORM_SUBMITTED", "event", "preventDefault", "reportEvents", "tableReload", "detail", "then", "addToast", "catch", "Notification", "exception", "NOSUBMIT_BUTTON_PRESSED", "pendingPromise", "Pending", "reportParameters", "parameter", "reportid", "parameters", "html", "js", "replaceNodeContents", "resolve", "style", "overflowX"], "mappings": ";;;;;;;kYAyCMA,qBAAuBC,MAAMC,cAAeC,qBACxCC,kBAAoBF,cAAcG,cAAcC,gBAAgBC,QAAQH,mBAG1EA,kBAAkBI,YADlBL,YAAc,QACwB,kBAAU,kBAAmB,qBAAsBA,mBAEnD,kBAAU,UAAW,yBAW/C,CAACM,SAAUC,mBACrBR,cAAgBS,SAASN,cAAcC,gBAAgBM,UAAUH,WACjEI,oBAAsBX,cAAcG,cAAcC,gBAAgBC,QAAQO,gBAG5ED,oBAAoBE,QAAQC,mBAGhCH,oBAAoBE,QAAQC,aAAc,QAEpCC,WAAa,IAAIC,qBAAYL,oBAAqB,sCAGxDI,WAAWE,iBAAiBF,WAAWG,OAAOC,gBAAgBC,QAC1DA,MAAMC,qDAGQC,aAAaC,YAAa,GAAIvB,eAC5CF,qBAAqBE,cAAeoB,MAAMI,2BAEhC,iBAAkB,sBACvBC,KAAKC,YACLC,MAAMC,sBAAaC,cAI5Bd,WAAWE,iBAAiBF,WAAWG,OAAOY,yBAAyBV,QACnEA,MAAMC,uBAEAU,eAAiB,IAAIC,iBAAQ,oCAC7BC,iBAAmBjC,cAAca,QAAQqB,oCAElC3B,SAAU0B,kBAClBR,MAAK,KAAM,kBAAU,eAAgB,wBACrCA,KAAKC,YACLD,MAAK,KAAM,0BAAa,qBAAsB,eAAgBjB,UAAW,CACtE2B,SAAU5B,SACV6B,WAAYH,qBAEfR,MAAK,CAACY,KAAMC,yBACCC,oBAAoB5B,oBAAqB0B,KAAMC,wCAE3ChB,aAAaC,YAAa,GAAIvB,eAC5CF,qBAAqBE,cAAe,GAE7B+B,eAAeS,aAEzBb,MAAMC,sBAAaC,cAI5BpB,SAASN,cAAc,gBAAgBsC,MAAMC,UAAY"}