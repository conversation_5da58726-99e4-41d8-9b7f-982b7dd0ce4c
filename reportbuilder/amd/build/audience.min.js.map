{"version": 3, "file": "audience.min.js", "sources": ["../src/audience.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder audiences\n *\n * @module      core_reportbuilder/audience\n * @copyright   2021 David <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport 'core/inplace_editable';\nimport Templates from 'core/templates';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport {getString} from 'core/str';\nimport DynamicForm from 'core_form/dynamicform';\nimport {add as addToast} from 'core/toast';\nimport {deleteAudience} from 'core_reportbuilder/local/repository/audiences';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {loadFragment} from 'core/fragment';\nimport {markFormAsDirty} from 'core_form/changechecker';\n\nlet reportId = 0;\nlet contextId = 0;\n\n/**\n * Add audience card\n *\n * @param {String} className\n * @param {String} title\n */\nconst addAudienceCard = (className, title) => {\n    const pendingPromise = new Pending('core_reportbuilder/audience:add');\n\n    const audiencesContainer = document.querySelector(reportSelectors.regions.audiencesContainer);\n    const audienceCardLength = audiencesContainer.querySelectorAll(reportSelectors.regions.audienceCard).length;\n\n    const params = {\n        classname: className,\n        reportid: reportId,\n        showormessage: (audienceCardLength > 0),\n        title: title,\n    };\n\n    // Load audience card fragment, render and then initialise the form within.\n    loadFragment('core_reportbuilder', 'audience_form', contextId, params)\n        .then((html, js) => {\n            const audienceCard = Templates.appendNodeContents(audiencesContainer, html, js)[0];\n            const audienceEmptyMessage = audiencesContainer.querySelector(reportSelectors.regions.audienceEmptyMessage);\n\n            const audienceForm = initAudienceCardForm(audienceCard);\n            // Mark as dirty new audience form created to prevent users leaving the page without saving it.\n            markFormAsDirty(audienceForm.getFormNode());\n            audienceEmptyMessage.classList.add('hidden');\n\n            return getString('audienceadded', 'core_reportbuilder', title);\n        })\n        .then(addToast)\n        .then(() => pendingPromise.resolve())\n        .catch(Notification.exception);\n};\n\n/**\n * Edit audience card\n *\n * @param {Element} audienceCard\n */\nconst editAudienceCard = audienceCard => {\n    const pendingPromise = new Pending('core_reportbuilder/audience:edit');\n\n    // Load audience form with data for editing, then toggle visible controls in the card.\n    const audienceForm = initAudienceCardForm(audienceCard);\n    audienceForm.load({id: audienceCard.dataset.audienceId})\n        .then(() => {\n            const audienceFormContainer = audienceCard.querySelector(reportSelectors.regions.audienceFormContainer);\n            const audienceDescription = audienceCard.querySelector(reportSelectors.regions.audienceDescription);\n            const audienceEdit = audienceCard.querySelector(reportSelectors.actions.audienceEdit);\n\n            audienceFormContainer.classList.remove('hidden');\n            audienceDescription.classList.add('hidden');\n            audienceEdit.disabled = true;\n\n            return pendingPromise.resolve();\n        })\n        .catch(Notification.exception);\n};\n\n/**\n * Initialise dynamic form within given audience card\n *\n * @param {Element} audienceCard\n * @return {DynamicForm}\n */\nconst initAudienceCardForm = audienceCard => {\n    const audienceFormContainer = audienceCard.querySelector(reportSelectors.regions.audienceFormContainer);\n    const audienceForm = new DynamicForm(audienceFormContainer, '\\\\core_reportbuilder\\\\form\\\\audience');\n\n    // After submitting the form, update the card instance and description properties.\n    audienceForm.addEventListener(audienceForm.events.FORM_SUBMITTED, data => {\n        const audienceHeading = audienceCard.querySelector(reportSelectors.regions.audienceHeading);\n        const audienceDescription = audienceCard.querySelector(reportSelectors.regions.audienceDescription);\n\n        audienceCard.dataset.audienceId = data.detail.instanceid;\n\n        audienceHeading.innerHTML = data.detail.heading;\n        audienceDescription.innerHTML = data.detail.description;\n\n        closeAudienceCardForm(audienceCard);\n\n        return getString('audiencesaved', 'core_reportbuilder')\n            .then(addToast);\n    });\n\n    // If cancelling the form, close the card or remove it if it was never created.\n    audienceForm.addEventListener(audienceForm.events.FORM_CANCELLED, () => {\n        if (audienceCard.dataset.audienceId > 0) {\n            closeAudienceCardForm(audienceCard);\n        } else {\n            removeAudienceCard(audienceCard);\n        }\n    });\n\n    return audienceForm;\n};\n\n/**\n * Delete audience card\n *\n * @param {Element} audienceDelete\n */\nconst deleteAudienceCard = audienceDelete => {\n    const audienceCard = audienceDelete.closest(reportSelectors.regions.audienceCard);\n    const {audienceId, audienceTitle, audienceEditWarning = false} = audienceCard.dataset;\n\n    // The edit warning indicates the audience is in use in a report schedule.\n    const audienceDeleteConfirmation = audienceEditWarning ? 'audienceusedbyschedule' : 'deleteaudienceconfirm';\n\n    Notification.saveCancelPromise(\n        getString('deleteaudience', 'core_reportbuilder', audienceTitle),\n        getString(audienceDeleteConfirmation, 'core_reportbuilder', audienceTitle),\n        getString('delete', 'core'),\n        {triggerElement: audienceDelete}\n    ).then(() => {\n        const pendingPromise = new Pending('core_reportbuilder/audience:delete');\n\n        return deleteAudience(reportId, audienceId)\n            .then(() => addToast(getString('audiencedeleted', 'core_reportbuilder', audienceTitle)))\n            .then(() => {\n                removeAudienceCard(audienceCard);\n                return pendingPromise.resolve();\n            })\n            .catch(Notification.exception);\n    }).catch(() => {\n        return;\n    });\n};\n\n/**\n * Close audience card form\n *\n * @param {Element} audienceCard\n */\nconst closeAudienceCardForm = audienceCard => {\n    // Remove the [data-region=\"audience-form-container\"] (with all the event listeners attached to it), and create it again.\n    const audienceFormContainer = audienceCard.querySelector(reportSelectors.regions.audienceFormContainer);\n    const NewAudienceFormContainer = audienceFormContainer.cloneNode(false);\n    audienceCard.querySelector(reportSelectors.regions.audienceForm).replaceChild(NewAudienceFormContainer, audienceFormContainer);\n    // Show the description container and enable the action buttons.\n    audienceCard.querySelector(reportSelectors.regions.audienceDescription).classList.remove('hidden');\n    audienceCard.querySelector(reportSelectors.actions.audienceEdit).disabled = false;\n    audienceCard.querySelector(reportSelectors.actions.audienceDelete).disabled = false;\n};\n\n/**\n * Remove audience card\n *\n * @param {Element} audienceCard\n */\nconst removeAudienceCard = audienceCard => {\n    audienceCard.remove();\n\n    const audiencesContainer = document.querySelector(reportSelectors.regions.audiencesContainer);\n    const audienceCards = audiencesContainer.querySelectorAll(reportSelectors.regions.audienceCard);\n\n    // Show message if there are no cards remaining, ensure first card's separator is not present.\n    if (audienceCards.length === 0) {\n        const audienceEmptyMessage = document.querySelector(reportSelectors.regions.audienceEmptyMessage);\n        audienceEmptyMessage.classList.remove('hidden');\n    } else {\n        const audienceFirstCardSeparator = audienceCards[0].querySelector('.audience-separator');\n        audienceFirstCardSeparator?.remove();\n    }\n};\n\nlet initialized = false;\n\n/**\n * Initialise audiences tab.\n *\n * @param {Number} id\n * @param {Number} contextid\n */\nexport const init = (id, contextid) => {\n    prefetchStrings('core_reportbuilder', [\n        'audienceadded',\n        'audiencedeleted',\n        'audiencesaved',\n        'audienceusedbyschedule',\n        'deleteaudience',\n        'deleteaudienceconfirm',\n    ]);\n\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    reportId = id;\n    contextId = contextid;\n\n    if (initialized) {\n        // We already added the event listeners (can be called multiple times by mustache template).\n        return;\n    }\n\n    document.addEventListener('click', event => {\n\n        // Add instance.\n        const audienceAdd = event.target.closest(reportSelectors.actions.audienceAdd);\n        if (audienceAdd) {\n            event.preventDefault();\n            addAudienceCard(audienceAdd.dataset.uniqueIdentifier, audienceAdd.dataset.name);\n        }\n\n        // Edit instance.\n        const audienceEdit = event.target.closest(reportSelectors.actions.audienceEdit);\n        if (audienceEdit) {\n            const audienceEditCard = audienceEdit.closest(reportSelectors.regions.audienceCard);\n\n            event.preventDefault();\n            editAudienceCard(audienceEditCard);\n        }\n\n        // Delete instance.\n        const audienceDelete = event.target.closest(reportSelectors.actions.audienceDelete);\n        if (audienceDelete) {\n            event.preventDefault();\n            deleteAudienceCard(audienceDelete);\n        }\n    });\n\n    initialized = true;\n};\n"], "names": ["reportId", "contextId", "initAudienceCardForm", "audienceCard", "audienceFormContainer", "querySelector", "reportSelectors", "regions", "audienceForm", "DynamicForm", "addEventListener", "events", "FORM_SUBMITTED", "data", "audienceHeading", "audienceDescription", "dataset", "audienceId", "detail", "instanceid", "innerHTML", "heading", "description", "closeAudienceCardForm", "then", "addToast", "FORM_CANCELLED", "removeAudienceCard", "NewAudienceFormContainer", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "classList", "remove", "actions", "audienceEdit", "disabled", "audienceDelete", "audienceCards", "document", "audiencesContainer", "querySelectorAll", "length", "audienceEmptyMessage", "audienceFirstCardSeparator", "initialized", "id", "contextid", "event", "audienceAdd", "target", "closest", "preventDefault", "className", "title", "pendingPromise", "Pending", "audienceCardLength", "params", "classname", "reportid", "showormessage", "html", "js", "Templates", "appendNodeContents", "getFormNode", "add", "resolve", "catch", "Notification", "exception", "addAudienceCard", "uniqueIdentifier", "name", "audienceEditCard", "load", "editAudienceCard", "audienceTitle", "audienceEditWarning", "audienceDeleteConfirmation", "saveCancelPromise", "triggerElement", "deleteAudienceCard"], "mappings": "6vDAsCIA,SAAW,EACXC,UAAY,QAsEVC,qBAAuBC,qBACnBC,sBAAwBD,aAAaE,cAAcC,gBAAgBC,QAAQH,uBAC3EI,aAAe,IAAIC,qBAAYL,sBAAuB,+CAG5DI,aAAaE,iBAAiBF,aAAaG,OAAOC,gBAAgBC,aACxDC,gBAAkBX,aAAaE,cAAcC,gBAAgBC,QAAQO,iBACrEC,oBAAsBZ,aAAaE,cAAcC,gBAAgBC,QAAQQ,4BAE/EZ,aAAaa,QAAQC,WAAaJ,KAAKK,OAAOC,WAE9CL,gBAAgBM,UAAYP,KAAKK,OAAOG,QACxCN,oBAAoBK,UAAYP,KAAKK,OAAOI,YAE5CC,sBAAsBpB,eAEf,kBAAU,gBAAiB,sBAC7BqB,KAAKC,eAIdjB,aAAaE,iBAAiBF,aAAaG,OAAOe,gBAAgB,KAC1DvB,aAAaa,QAAQC,WAAa,EAClCM,sBAAsBpB,cAEtBwB,mBAAmBxB,iBAIpBK,cAwCLe,sBAAwBpB,qBAEpBC,sBAAwBD,aAAaE,cAAcC,gBAAgBC,QAAQH,uBAC3EwB,yBAA2BxB,sBAAsByB,WAAU,GACjE1B,aAAaE,cAAcC,gBAAgBC,QAAQC,cAAcsB,aAAaF,yBAA0BxB,uBAExGD,aAAaE,cAAcC,gBAAgBC,QAAQQ,qBAAqBgB,UAAUC,OAAO,UACzF7B,aAAaE,cAAcC,gBAAgB2B,QAAQC,cAAcC,UAAW,EAC5EhC,aAAaE,cAAcC,gBAAgB2B,QAAQG,gBAAgBD,UAAW,GAQ5ER,mBAAqBxB,eACvBA,aAAa6B,eAGPK,cADqBC,SAASjC,cAAcC,gBAAgBC,QAAQgC,oBACjCC,iBAAiBlC,gBAAgBC,QAAQJ,iBAGrD,IAAzBkC,cAAcI,OAAc,CACCH,SAASjC,cAAcC,gBAAgBC,QAAQmC,sBACvDX,UAAUC,OAAO,cACnC,OACGW,2BAA6BN,cAAc,GAAGhC,cAAc,uBAClEsC,MAAAA,4BAAAA,2BAA4BX,eAIhCY,aAAc,gBAQE,CAACC,GAAIC,2CACL,qBAAsB,CAClC,gBACA,kBACA,gBACA,yBACA,iBACA,wDAGY,OAAQ,CACpB,WAGJ9C,SAAW6C,GACX5C,UAAY6C,UAERF,cAKJN,SAAS5B,iBAAiB,SAASqC,cAGzBC,YAAcD,MAAME,OAAOC,QAAQ5C,gBAAgB2B,QAAQe,aAC7DA,cACAD,MAAMI,iBAtMM,EAACC,UAAWC,eAC1BC,eAAiB,IAAIC,iBAAQ,mCAE7BhB,mBAAqBD,SAASjC,cAAcC,gBAAgBC,QAAQgC,oBACpEiB,mBAAqBjB,mBAAmBC,iBAAiBlC,gBAAgBC,QAAQJ,cAAcsC,OAE/FgB,OAAS,CACXC,UAAWN,UACXO,SAAU3D,SACV4D,cAAgBJ,mBAAqB,EACrCH,MAAOA,kCAIE,qBAAsB,gBAAiBpD,UAAWwD,QAC1DjC,MAAK,CAACqC,KAAMC,YACH3D,aAAe4D,mBAAUC,mBAAmBzB,mBAAoBsB,KAAMC,IAAI,GAC1EpB,qBAAuBH,mBAAmBlC,cAAcC,gBAAgBC,QAAQmC,sBAEhFlC,aAAeN,qBAAqBC,uDAE1BK,aAAayD,eAC7BvB,qBAAqBX,UAAUmC,IAAI,WAE5B,kBAAU,gBAAiB,qBAAsBb,UAE3D7B,KAAKC,YACLD,MAAK,IAAM8B,eAAea,YAC1BC,MAAMC,sBAAaC,YA2KhBC,CAAgBvB,YAAYhC,QAAQwD,iBAAkBxB,YAAYhC,QAAQyD,aAIxEvC,aAAea,MAAME,OAAOC,QAAQ5C,gBAAgB2B,QAAQC,iBAC9DA,aAAc,OACRwC,iBAAmBxC,aAAagB,QAAQ5C,gBAAgBC,QAAQJ,cAEtE4C,MAAMI,iBA3KOhD,CAAAA,qBACfmD,eAAiB,IAAIC,iBAAQ,oCAGdrD,qBAAqBC,cAC7BwE,KAAK,CAAC9B,GAAI1C,aAAaa,QAAQC,aACvCO,MAAK,WACIpB,sBAAwBD,aAAaE,cAAcC,gBAAgBC,QAAQH,uBAC3EW,oBAAsBZ,aAAaE,cAAcC,gBAAgBC,QAAQQ,qBACzEmB,aAAe/B,aAAaE,cAAcC,gBAAgB2B,QAAQC,qBAExE9B,sBAAsB2B,UAAUC,OAAO,UACvCjB,oBAAoBgB,UAAUmC,IAAI,UAClChC,aAAaC,UAAW,EAEjBmB,eAAea,aAEzBC,MAAMC,sBAAaC,YA2JhBM,CAAiBF,wBAIftC,eAAiBW,MAAME,OAAOC,QAAQ5C,gBAAgB2B,QAAQG,gBAChEA,iBACAW,MAAMI,iBAnHSf,CAAAA,uBACjBjC,aAAeiC,eAAec,QAAQ5C,gBAAgBC,QAAQJ,eAC9Dc,WAACA,WAAD4D,cAAaA,cAAbC,oBAA4BA,qBAAsB,GAAS3E,aAAaa,QAGxE+D,2BAA6BD,oBAAsB,yBAA2B,8CAEvEE,mBACT,kBAAU,iBAAkB,qBAAsBH,gBAClD,kBAAUE,2BAA4B,qBAAsBF,gBAC5D,kBAAU,SAAU,QACpB,CAACI,eAAgB7C,iBACnBZ,MAAK,WACG8B,eAAiB,IAAIC,iBAAQ,6CAE5B,6BAAevD,SAAUiB,YAC3BO,MAAK,KAAM,eAAS,kBAAU,kBAAmB,qBAAsBqD,kBACvErD,MAAK,KACFG,mBAAmBxB,cACZmD,eAAea,aAEzBC,MAAMC,sBAAaC,cACzBF,OAAM,UA8FDc,CAAmB9C,oBAI3BQ,aAAc"}