define("core_reportbuilder/reports_list",["exports","core/event_dispatcher","core/notification","core/pending","core/prefetch","core/str","core/toast","core_reportbuilder/local/events","core_reportbuilder/local/selectors","core_reportbuilder/local/repository/reports","core_reportbuilder/local/repository/modals"],(function(_exports,_event_dispatcher,_notification,_pending,_prefetch,_str,_toast,reportEvents,reportSelectors,_reports,_modals){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors);_exports.init=()=>{(0,_prefetch.prefetchStrings)("core_reportbuilder",["deletereport","deletereportconfirm","editreportdetails","newreport","reportdeleted","reportupdated"]),(0,_prefetch.prefetchStrings)("core",["delete"]),document.addEventListener("click",(event=>{if(event.target.closest(reportSelectors.actions.reportCreate)){event.preventDefault();const reportModal=(0,_modals.createReportModal)(event.target,(0,_str.getString)("newreport","core_reportbuilder"));reportModal.addEventListener(reportModal.events.FORM_SUBMITTED,(event=>{window.location.href=event.detail})),reportModal.show()}const reportEdit=event.target.closest(reportSelectors.actions.reportEdit);if(reportEdit){event.preventDefault();const triggerElement=reportEdit.closest(".dropdown").querySelector(".dropdown-toggle"),reportModal=(0,_modals.createReportModal)(triggerElement,(0,_str.getString)("editreportdetails","core_reportbuilder"),reportEdit.dataset.reportId);reportModal.addEventListener(reportModal.events.FORM_SUBMITTED,(()=>{const reportElement=event.target.closest(reportSelectors.regions.report);(0,_str.getString)("reportupdated","core_reportbuilder").then(_toast.add).then((()=>{(0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement)})).catch(_notification.default.exception)})),reportModal.show()}const reportDelete=event.target.closest(reportSelectors.actions.reportDelete);if(reportDelete){event.preventDefault();const triggerElement=reportDelete.closest(".dropdown").querySelector(".dropdown-toggle");_notification.default.saveCancelPromise((0,_str.getString)("deletereport","core_reportbuilder"),(0,_str.getString)("deletereportconfirm","core_reportbuilder",reportDelete.dataset.reportName),(0,_str.getString)("delete","core"),{triggerElement:triggerElement}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/reports:delete"),reportElement=event.target.closest(reportSelectors.regions.report);return(0,_reports.deleteReport)(reportDelete.dataset.reportId).then((()=>(0,_toast.add)((0,_str.getString)("reportdeleted","core_reportbuilder")))).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{preservePagination:!0},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)})).catch((()=>{}))}}))}}));

//# sourceMappingURL=reports_list.min.js.map