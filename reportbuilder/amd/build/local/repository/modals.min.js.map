{"version": 3, "file": "modals.min.js", "sources": ["../../../src/local/repository/modals.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle modal form requests\n *\n * @module      core_reportbuilder/local/repository/modals\n * @copyright   2021 David <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport ModalForm from 'core_form/modalform';\nimport {getString} from 'core/str';\n\n/**\n * Return modal instance\n *\n * @param {EventTarget} triggerElement\n * @param {Promise} modalTitle\n * @param {String} formClass\n * @param {Object} formArgs\n * @return {ModalForm}\n */\nconst createModalForm = (triggerElement, modalTitle, formClass, formArgs) => {\n    return new ModalForm({\n        modalConfig: {\n            title: modalTitle,\n        },\n        formClass: formClass,\n        args: formArgs,\n        saveButtonText: getString('save', 'moodle'),\n        returnFocus: triggerElement,\n    });\n};\n\n/**\n * Return report modal instance\n *\n * @param {EventTarget} triggerElement\n * @param {Promise} modalTitle\n * @param {Number} reportId\n * @return {ModalForm}\n */\nexport const createReportModal = (triggerElement, modalTitle, reportId = 0) => {\n    return createModalForm(triggerElement, modalTitle, 'core_reportbuilder\\\\form\\\\report', {\n        id: reportId,\n    });\n};\n\n/**\n * Return schedule modal instance\n *\n * @param {EventTarget} triggerElement\n * @param {Promise} modalTitle\n * @param {Number} reportId\n * @param {Number} scheduleId\n * @return {ModalForm}\n */\nexport const createScheduleModal = (triggerElement, modalTitle, reportId, scheduleId = 0) => {\n    return createModalForm(triggerElement, modalTitle, 'core_reportbuilder\\\\form\\\\schedule', {\n        reportid: reportId,\n        id: scheduleId,\n    });\n};\n"], "names": ["createModalForm", "triggerElement", "modalTitle", "formClass", "formArgs", "ModalForm", "modalConfig", "title", "args", "saveButtonText", "returnFocus", "reportId", "id", "scheduleId", "reportid"], "mappings": ";;;;;;;gMAmCMA,gBAAkB,CAACC,eAAgBC,WAAYC,UAAWC,WACrD,IAAIC,mBAAU,CACjBC,YAAa,CACTC,MAAOL,YAEXC,UAAWA,UACXK,KAAMJ,SACNK,gBAAgB,kBAAU,OAAQ,UAClCC,YAAaT,4CAYY,SAACA,eAAgBC,gBAAYS,gEAAW,SAC9DX,gBAAgBC,eAAgBC,WAAY,mCAAoC,CACnFU,GAAID,yCAauB,SAACV,eAAgBC,WAAYS,cAAUE,kEAAa,SAC5Eb,gBAAgBC,eAAgBC,WAAY,qCAAsC,CACrFY,SAAUH,SACVC,GAAIC"}