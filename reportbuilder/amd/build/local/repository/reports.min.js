define("core_reportbuilder/local/repository/reports",["exports","core/ajax"],(function(_exports,_ajax){var obj;
/**
   * Module to handle report AJAX requests
   *
   * @module      core_reportbuilder/local/repository/reports
   * @copyright   2021 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.getReport=_exports.deleteReport=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};_exports.deleteReport=reportId=>{const request={methodname:"core_reportbuilder_reports_delete",args:{reportid:reportId}};return _ajax.default.call([request])[0]};_exports.getReport=function(reportId,editMode){let pageSize=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const request={methodname:"core_reportbuilder_reports_get",args:{reportid:reportId,editmode:editMode,pagesize:pageSize}};return _ajax.default.call([request])[0]}}));

//# sourceMappingURL=reports.min.js.map