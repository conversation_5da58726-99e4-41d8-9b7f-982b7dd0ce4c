define("core_reportbuilder/local/editor/conditions",["exports","jquery","core/event_dispatcher","core/form-autocomplete","core/inplace_editable","core/notification","core/pending","core/prefetch","core/sortable_list","core/str","core/templates","core/toast","core_form/dynamicform","core_reportbuilder/local/events","core_reportbuilder/local/selectors","core_reportbuilder/local/repository/conditions"],(function(_exports,_jquery,_event_dispatcher,_formAutocomplete,_inplace_editable,_notification,_pending,_prefetch,_sortable_list,_str,_templates,_toast,_dynamicform,reportEvents,reportSelectors,_conditions){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_formAutocomplete=_interopRequireDefault(_formAutocomplete),_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),_sortable_list=_interopRequireDefault(_sortable_list),_templates=_interopRequireDefault(_templates),_dynamicform=_interopRequireDefault(_dynamicform),reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors);const reloadSettingsConditionsRegion=(reportElement,templateContext)=>{const pendingPromise=new _pending.default("core_reportbuilder/conditions:reload"),settingsConditionsRegion=reportElement.querySelector(reportSelectors.regions.settingsConditions);return _templates.default.renderForPromise("core_reportbuilder/local/settings/conditions",{conditions:templateContext}).then((_ref=>{let{html:html,js:js}=_ref;const conditionsjs=_jquery.default.parseHTML(templateContext.javascript,null,!0).map((node=>node.innerHTML)).join("\n");_templates.default.replaceNode(settingsConditionsRegion,html,js+conditionsjs),initConditionsForm();const reportAddCondition=reportElement.querySelector(reportSelectors.actions.reportAddCondition);return null==reportAddCondition||reportAddCondition.focus(),pendingPromise.resolve()}))},initConditionsForm=()=>{const reportElement=document.querySelector(reportSelectors.regions.report),reportAddCondition=reportElement.querySelector(reportSelectors.actions.reportAddCondition);_formAutocomplete.default.enhanceField(reportAddCondition,!1,"",(0,_str.getString)("selectacondition","core_reportbuilder")).catch(_notification.default.exception);const conditionFormContainer=reportElement.querySelector(reportSelectors.regions.settingsConditions);if(!conditionFormContainer)return;const conditionForm=new _dynamicform.default(conditionFormContainer,"\\core_reportbuilder\\form\\condition");conditionForm.addEventListener(conditionForm.events.FORM_SUBMITTED,(event=>{event.preventDefault(),(0,_str.getString)("conditionsapplied","core_reportbuilder").then(_toast.add).catch(_notification.default.exception),(0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement)})),conditionForm.addEventListener(conditionForm.events.NOSUBMIT_BUTTON_PRESSED,(event=>{event.preventDefault(),_notification.default.saveCancelPromise((0,_str.getString)("resetconditions","core_reportbuilder"),(0,_str.getString)("resetconditionsconfirm","core_reportbuilder"),(0,_str.getString)("resetall","core_reportbuilder"),{triggerElement:event.detail}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/conditions:reset");return(0,_conditions.resetConditions)(reportElement.dataset.reportId).then((data=>reloadSettingsConditionsRegion(reportElement,data))).then((()=>(0,_toast.add)((0,_str.getString)("conditionsreset","core_reportbuilder")))).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)})).catch((()=>{}))}))};_exports.init=initialized=>{((0,_prefetch.prefetchStrings)("core_reportbuilder",["conditionadded","conditiondeleted","conditionmoved","conditionsapplied","conditionsreset","deletecondition","deleteconditionconfirm","resetall","resetconditions","resetconditionsconfirm","selectacondition"]),(0,_prefetch.prefetchStrings)("core",["delete"]),initConditionsForm(),initialized)||(document.addEventListener("change",(event=>{const reportAddCondition=event.target.closest(reportSelectors.actions.reportAddCondition);if(reportAddCondition){if(event.preventDefault(),""===reportAddCondition.value||"0"===reportAddCondition.value)return;const reportElement=reportAddCondition.closest(reportSelectors.regions.report),pendingPromise=new _pending.default("core_reportbuilder/conditions:add");(0,_conditions.addCondition)(reportElement.dataset.reportId,reportAddCondition.value).then((data=>reloadSettingsConditionsRegion(reportElement,data))).then((()=>(0,_str.getString)("conditionadded","core_reportbuilder",reportAddCondition.options[reportAddCondition.selectedIndex].text))).then(_toast.add).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)}})),document.addEventListener("click",(event=>{const reportRemoveCondition=event.target.closest(reportSelectors.actions.reportRemoveCondition);if(reportRemoveCondition){event.preventDefault();const reportElement=reportRemoveCondition.closest(reportSelectors.regions.report),conditionContainer=reportRemoveCondition.closest(reportSelectors.regions.activeCondition),conditionName=conditionContainer.dataset.conditionName;_notification.default.saveCancelPromise((0,_str.getString)("deletecondition","core_reportbuilder",conditionName),(0,_str.getString)("deleteconditionconfirm","core_reportbuilder",conditionName),(0,_str.getString)("delete","core"),{triggerElement:reportRemoveCondition}).then((()=>{const pendingPromise=new _pending.default("core_reportbuilder/conditions:remove");return(0,_conditions.deleteCondition)(reportElement.dataset.reportId,conditionContainer.dataset.conditionId).then((data=>reloadSettingsConditionsRegion(reportElement,data))).then((()=>(0,_toast.add)((0,_str.getString)("conditiondeleted","core_reportbuilder",conditionName)))).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)})).catch((()=>{}))}})),new _sortable_list.default("".concat(reportSelectors.regions.activeConditions),{isHorizontal:!1}).getElementName=element=>Promise.resolve(element.data("conditionName")),(0,_jquery.default)(document).on(_sortable_list.default.EVENTS.DROP,reportSelectors.regions.activeCondition,((event,info)=>{if(info.positionChanged){const pendingPromise=new _pending.default("core_reportbuilder/conditions:reorder"),reportElement=event.target.closest(reportSelectors.regions.report),conditionId=info.element.data("conditionId"),conditionPosition=info.element.data("conditionPosition");let targetConditionPosition=info.targetNextElement.data("conditionPosition")||info.element.siblings().length+2;targetConditionPosition>conditionPosition&&targetConditionPosition--;const reorderPromise=(0,_conditions.reorderCondition)(reportElement.dataset.reportId,conditionId,targetConditionPosition);Promise.all([reorderPromise,new Promise((resolve=>setTimeout(resolve,1e3)))]).then((_ref2=>{let[data]=_ref2;return reloadSettingsConditionsRegion(reportElement,data)})).then((()=>(0,_str.getString)("conditionmoved","core_reportbuilder",info.element.data("conditionName")))).then(_toast.add).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)}})))}}));

//# sourceMappingURL=conditions.min.js.map