{"version": 3, "file": "card_view.min.js", "sources": ["../../../src/local/editor/card_view.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder card view editor\n *\n * @module      core_reportbuilder/local/editor/card_view\n * @copyright   2021 Mikel Martín <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport DynamicForm from 'core_form/dynamicform';\nimport {add as addToast} from 'core/toast';\nimport {getString} from \"core/str\";\nimport {subscribe as subscribe} from 'core/pubsub';\nimport Notification from 'core/notification';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\n\nlet cardViewForm = null;\n\n/**\n * Initialise card view form, must be called on each init because the form container is re-created when switching editor modes\n */\nconst initCardViewForm = () => {\n    const cardViewFormContainer = document.querySelector(reportSelectors.regions.settingsCardView);\n    if (!cardViewFormContainer) {\n        return;\n    }\n    cardViewForm = new DynamicForm(cardViewFormContainer, '\\\\core_reportbuilder\\\\form\\\\card_view');\n\n    cardViewForm.addEventListener(cardViewForm.events.FORM_SUBMITTED, (event) => {\n        event.preventDefault();\n\n        getString('cardviewsettingssaved', 'core_reportbuilder')\n            .then(addToast)\n            .catch(Notification.exception);\n    });\n};\n\n/**\n * Initialise module\n *\n * @param {Boolean} initialized Ensure we only add our listeners once\n */\nexport const init = (initialized) => {\n    initCardViewForm();\n    if (initialized) {\n        return;\n    }\n\n    // Update form each time a column is added or removed to the custom report.\n    subscribe(reportEvents.publish.reportColumnsUpdated, () => {\n        const reportElement = document.querySelector(reportSelectors.regions.report);\n        cardViewForm.load({reportid: reportElement.dataset.reportId});\n    });\n};\n"], "names": ["cardViewForm", "initialized", "cardViewFormContainer", "document", "querySelector", "reportSelectors", "regions", "settingsCardView", "DynamicForm", "addEventListener", "events", "FORM_SUBMITTED", "event", "preventDefault", "then", "addToast", "catch", "Notification", "exception", "initCardViewForm", "reportEvents", "publish", "reportColumnsUpdated", "reportElement", "report", "load", "reportid", "dataset", "reportId"], "mappings": "omDAiCIA,aAAe,mBA0BEC,cArBI,YACfC,sBAAwBC,SAASC,cAAcC,gBAAgBC,QAAQC,kBACxEL,wBAGLF,aAAe,IAAIQ,qBAAYN,sBAAuB,yCAEtDF,aAAaS,iBAAiBT,aAAaU,OAAOC,gBAAiBC,QAC/DA,MAAMC,oCAEI,wBAAyB,sBAC9BC,KAAKC,YACLC,MAAMC,sBAAaC,gBAU5BC,GACIlB,mCAKMmB,aAAaC,QAAQC,sBAAsB,WAC3CC,cAAgBpB,SAASC,cAAcC,gBAAgBC,QAAQkB,QACrExB,aAAayB,KAAK,CAACC,SAAUH,cAAcI,QAAQC"}