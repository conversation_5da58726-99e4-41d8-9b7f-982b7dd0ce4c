{"version": 3, "file": "filters.min.js", "sources": ["../../../src/local/editor/filters.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder filters editor\n *\n * @module      core_reportbuilder/local/editor/filters\n * @copyright   2021 David Matamoros <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport $ from 'jquery';\nimport AutoComplete from 'core/form-autocomplete';\nimport 'core/inplace_editable';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport SortableList from 'core/sortable_list';\nimport {getString} from 'core/str';\nimport Templates from 'core/templates';\nimport {add as addToast} from 'core/toast';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {addFilter, deleteFilter, reorderFilter} from 'core_reportbuilder/local/repository/filters';\n\n/**\n * Reload filters settings region\n *\n * @param {Element} reportElement\n * @param {Object} templateContext\n * @return {Promise}\n */\nconst reloadSettingsFiltersRegion = (reportElement, templateContext) => {\n    const pendingPromise = new Pending('core_reportbuilder/filters:reload');\n    const settingsFiltersRegion = reportElement.querySelector(reportSelectors.regions.settingsFilters);\n\n    return Templates.renderForPromise('core_reportbuilder/local/settings/filters', {filters: templateContext})\n        .then(({html, js}) => {\n            Templates.replaceNode(settingsFiltersRegion, html, js);\n\n            initFiltersForm();\n\n            // Re-focus the add filter element after reloading the region.\n            const reportAddFilter = reportElement.querySelector(reportSelectors.actions.reportAddFilter);\n            reportAddFilter?.focus();\n\n            return pendingPromise.resolve();\n        });\n};\n\n/**\n * Initialise filters form, must be called on each init because the form container is re-created when switching editor modes\n */\nconst initFiltersForm = () => {\n    const reportElement = document.querySelector(reportSelectors.regions.report);\n\n    // Enhance filter selector.\n    const reportAddFilter = reportElement.querySelector(reportSelectors.actions.reportAddFilter);\n    AutoComplete.enhanceField(reportAddFilter, false, '', getString('selectafilter', 'core_reportbuilder'))\n        .catch(Notification.exception);\n};\n\n/**\n * Initialise module, prefetch all required strings\n *\n * @param {Boolean} initialized Ensure we only add our listeners once\n */\nexport const init = initialized => {\n    prefetchStrings('core_reportbuilder', [\n        'deletefilter',\n        'deletefilterconfirm',\n        'filteradded',\n        'filterdeleted',\n        'filtermoved',\n        'selectafilter',\n    ]);\n\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    initFiltersForm();\n    if (initialized) {\n        return;\n    }\n\n    // Add filter to report.\n    document.addEventListener('change', event => {\n        const reportAddFilter = event.target.closest(reportSelectors.actions.reportAddFilter);\n        if (reportAddFilter) {\n            event.preventDefault();\n\n            // Check if dropdown is closed with no filter selected.\n            if (reportAddFilter.value === \"\" || reportAddFilter.value === \"0\") {\n                return;\n            }\n\n            const reportElement = reportAddFilter.closest(reportSelectors.regions.report);\n            const pendingPromise = new Pending('core_reportbuilder/filters:add');\n\n            addFilter(reportElement.dataset.reportId, reportAddFilter.value)\n                .then(data => reloadSettingsFiltersRegion(reportElement, data))\n                .then(() => getString('filteradded', 'core_reportbuilder',\n                    reportAddFilter.options[reportAddFilter.selectedIndex].text))\n                .then(addToast)\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n    });\n\n    document.addEventListener('click', event => {\n\n        // Remove filter from report.\n        const reportRemoveFilter = event.target.closest(reportSelectors.actions.reportRemoveFilter);\n        if (reportRemoveFilter) {\n            event.preventDefault();\n\n            const reportElement = reportRemoveFilter.closest(reportSelectors.regions.report);\n            const filterContainer = reportRemoveFilter.closest(reportSelectors.regions.activeFilter);\n            const filterName = filterContainer.dataset.filterName;\n\n            Notification.saveCancelPromise(\n                getString('deletefilter', 'core_reportbuilder', filterName),\n                getString('deletefilterconfirm', 'core_reportbuilder', filterName),\n                getString('delete', 'core'),\n                {triggerElement: reportRemoveFilter}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/filters:remove');\n\n                return deleteFilter(reportElement.dataset.reportId, filterContainer.dataset.filterId)\n                    .then(data => reloadSettingsFiltersRegion(reportElement, data))\n                    .then(() => addToast(getString('filterdeleted', 'core_reportbuilder', filterName)))\n                    .then(() => pendingPromise.resolve())\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n\n    // Initialize sortable list to handle active filters moving (note JQuery dependency, see MDL-72293 for resolution).\n    var activeFiltersSortableList = new SortableList(`${reportSelectors.regions.activeFilters} ul`, {isHorizontal: false});\n    activeFiltersSortableList.getElementName = element => Promise.resolve(element.data('filterName'));\n\n    $(document).on(SortableList.EVENTS.DROP, `${reportSelectors.regions.report} li[data-filter-id]`, (event, info) => {\n        if (info.positionChanged) {\n            const pendingPromise = new Pending('core_reportbuilder/filters:reorder');\n            const reportElement = event.target.closest(reportSelectors.regions.report);\n            const filterId = info.element.data('filterId');\n            const filterPosition = info.element.data('filterPosition');\n\n            // Select target position, if moving to the end then count number of element siblings.\n            let targetFilterPosition = info.targetNextElement.data('filterPosition') || info.element.siblings().length + 2;\n            if (targetFilterPosition > filterPosition) {\n                targetFilterPosition--;\n            }\n\n            // Re-order filter, giving drop event transition time to finish.\n            const reorderPromise = reorderFilter(reportElement.dataset.reportId, filterId, targetFilterPosition);\n            Promise.all([reorderPromise, new Promise(resolve => setTimeout(resolve, 1000))])\n                .then(([data]) => reloadSettingsFiltersRegion(reportElement, data))\n                .then(() => getString('filtermoved', 'core_reportbuilder', info.element.data('filterName')))\n                .then(addToast)\n                .then(() => pendingPromise.resolve())\n                .catch(Notification.exception);\n        }\n    });\n};\n"], "names": ["reloadSettingsFiltersRegion", "reportElement", "templateContext", "pendingPromise", "Pending", "settingsFiltersRegion", "querySelector", "reportSelectors", "regions", "settingsFilters", "Templates", "renderForPromise", "filters", "then", "_ref", "html", "js", "replaceNode", "initFiltersForm", "reportAdd<PERSON><PERSON><PERSON>", "actions", "focus", "resolve", "document", "report", "enhanceField", "catch", "Notification", "exception", "initialized", "addEventListener", "event", "target", "closest", "preventDefault", "value", "dataset", "reportId", "data", "options", "selectedIndex", "text", "addToast", "reportRemoveFilter", "filterContainer", "activeFilter", "filterName", "saveCancelPromise", "triggerElement", "filterId", "SortableList", "activeFilters", "isHorizontal", "getElementName", "element", "Promise", "on", "EVENTS", "DROP", "info", "positionChanged", "filterPosition", "targetFilterPosition", "targetNextElement", "siblings", "length", "reorderPromise", "all", "setTimeout", "_ref2"], "mappings": "u2DA6CMA,4BAA8B,CAACC,cAAeC,yBAC1CC,eAAiB,IAAIC,iBAAQ,qCAC7BC,sBAAwBJ,cAAcK,cAAcC,gBAAgBC,QAAQC,wBAE3EC,mBAAUC,iBAAiB,4CAA6C,CAACC,QAASV,kBACpFW,MAAKC,WAACC,KAACA,KAADC,GAAOA,4BACAC,YAAYZ,sBAAuBU,KAAMC,IAEnDE,wBAGMC,gBAAkBlB,cAAcK,cAAcC,gBAAgBa,QAAQD,wBAC5EA,MAAAA,iBAAAA,gBAAiBE,QAEVlB,eAAemB,cAO5BJ,gBAAkB,WAIdC,gBAHgBI,SAASjB,cAAcC,gBAAgBC,QAAQgB,QAG/BlB,cAAcC,gBAAgBa,QAAQD,2CAC/DM,aAAaN,iBAAiB,EAAO,IAAI,kBAAU,gBAAiB,uBAC5EO,MAAMC,sBAAaC,0BAQRC,6CACA,qBAAsB,CAClC,eACA,sBACA,cACA,gBACA,cACA,gDAGY,OAAQ,CACpB,WAGJX,kBACIW,eAKJN,SAASO,iBAAiB,UAAUC,cAC1BZ,gBAAkBY,MAAMC,OAAOC,QAAQ1B,gBAAgBa,QAAQD,oBACjEA,gBAAiB,IACjBY,MAAMG,iBAGwB,KAA1Bf,gBAAgBgB,OAA0C,MAA1BhB,gBAAgBgB,mBAI9ClC,cAAgBkB,gBAAgBc,QAAQ1B,gBAAgBC,QAAQgB,QAChErB,eAAiB,IAAIC,iBAAQ,yDAEzBH,cAAcmC,QAAQC,SAAUlB,gBAAgBgB,OACrDtB,MAAKyB,MAAQtC,4BAA4BC,cAAeqC,QACxDzB,MAAK,KAAM,kBAAU,cAAe,qBACjCM,gBAAgBoB,QAAQpB,gBAAgBqB,eAAeC,QAC1D5B,KAAK6B,YACL7B,MAAK,IAAMV,eAAemB,YAC1BI,MAAMC,sBAAaC,eAIhCL,SAASO,iBAAiB,SAASC,cAGzBY,mBAAqBZ,MAAMC,OAAOC,QAAQ1B,gBAAgBa,QAAQuB,uBACpEA,mBAAoB,CACpBZ,MAAMG,uBAEAjC,cAAgB0C,mBAAmBV,QAAQ1B,gBAAgBC,QAAQgB,QACnEoB,gBAAkBD,mBAAmBV,QAAQ1B,gBAAgBC,QAAQqC,cACrEC,WAAaF,gBAAgBR,QAAQU,iCAE9BC,mBACT,kBAAU,eAAgB,qBAAsBD,aAChD,kBAAU,sBAAuB,qBAAsBA,aACvD,kBAAU,SAAU,QACpB,CAACE,eAAgBL,qBACnB9B,MAAK,WACGV,eAAiB,IAAIC,iBAAQ,4CAE5B,yBAAaH,cAAcmC,QAAQC,SAAUO,gBAAgBR,QAAQa,UACvEpC,MAAKyB,MAAQtC,4BAA4BC,cAAeqC,QACxDzB,MAAK,KAAM,eAAS,kBAAU,gBAAiB,qBAAsBiC,eACrEjC,MAAK,IAAMV,eAAemB,YAC1BI,MAAMC,sBAAaC,cACzBF,OAAM,aAOe,IAAIwB,iCAAgB3C,gBAAgBC,QAAQ2C,qBAAoB,CAACC,cAAc,IACrFC,eAAiBC,SAAWC,QAAQjC,QAAQgC,QAAQhB,KAAK,mCAEjFf,UAAUiC,GAAGN,uBAAaO,OAAOC,eAASnD,gBAAgBC,QAAQgB,+BAA6B,CAACO,MAAO4B,WACjGA,KAAKC,gBAAiB,OAChBzD,eAAiB,IAAIC,iBAAQ,sCAC7BH,cAAgB8B,MAAMC,OAAOC,QAAQ1B,gBAAgBC,QAAQgB,QAC7DyB,SAAWU,KAAKL,QAAQhB,KAAK,YAC7BuB,eAAiBF,KAAKL,QAAQhB,KAAK,sBAGrCwB,qBAAuBH,KAAKI,kBAAkBzB,KAAK,mBAAqBqB,KAAKL,QAAQU,WAAWC,OAAS,EACzGH,qBAAuBD,gBACvBC,6BAIEI,gBAAiB,0BAAcjE,cAAcmC,QAAQC,SAAUY,SAAUa,sBAC/EP,QAAQY,IAAI,CAACD,eAAgB,IAAIX,SAAQjC,SAAW8C,WAAW9C,QAAS,SACnET,MAAKwD,YAAE/B,mBAAUtC,4BAA4BC,cAAeqC,SAC5DzB,MAAK,KAAM,kBAAU,cAAe,qBAAsB8C,KAAKL,QAAQhB,KAAK,iBAC5EzB,KAAK6B,YACL7B,MAAK,IAAMV,eAAemB,YAC1BI,MAAMC,sBAAaC"}