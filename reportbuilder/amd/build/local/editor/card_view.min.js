define("core_reportbuilder/local/editor/card_view",["exports","core_form/dynamicform","core/toast","core/str","core/pubsub","core/notification","core_reportbuilder/local/events","core_reportbuilder/local/selectors"],(function(_exports,_dynamicform,_toast,_str,_pubsub,_notification,reportEvents,reportSelectors){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_dynamicform=_interopRequireDefault(_dynamicform),_notification=_interopRequireDefault(_notification),reportEvents=_interopRequireWildcard(reportEvents),reportSelectors=_interopRequireWildcard(reportSelectors);let cardViewForm=null;_exports.init=initialized=>{(()=>{const cardViewFormContainer=document.querySelector(reportSelectors.regions.settingsCardView);cardViewFormContainer&&(cardViewForm=new _dynamicform.default(cardViewFormContainer,"\\core_reportbuilder\\form\\card_view"),cardViewForm.addEventListener(cardViewForm.events.FORM_SUBMITTED,(event=>{event.preventDefault(),(0,_str.getString)("cardviewsettingssaved","core_reportbuilder").then(_toast.add).catch(_notification.default.exception)})))})(),initialized||(0,_pubsub.subscribe)(reportEvents.publish.reportColumnsUpdated,(()=>{const reportElement=document.querySelector(reportSelectors.regions.report);cardViewForm.load({reportid:reportElement.dataset.reportId})}))}}));

//# sourceMappingURL=card_view.min.js.map