{"version": 3, "file": "conditions.min.js", "sources": ["../../../src/local/editor/conditions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Report builder conditions editor\n *\n * @module      core_reportbuilder/local/editor/conditions\n * @copyright   2021 Paul Holden <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n\"use strict\";\n\nimport $ from 'jquery';\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport AutoComplete from 'core/form-autocomplete';\nimport 'core/inplace_editable';\nimport Notification from 'core/notification';\nimport Pending from 'core/pending';\nimport {prefetchStrings} from 'core/prefetch';\nimport SortableList from 'core/sortable_list';\nimport {getString} from 'core/str';\nimport Templates from 'core/templates';\nimport {add as addToast} from 'core/toast';\nimport DynamicForm from 'core_form/dynamicform';\nimport * as reportEvents from 'core_reportbuilder/local/events';\nimport * as reportSelectors from 'core_reportbuilder/local/selectors';\nimport {addCondition, deleteCondition, reorderCondition, resetConditions} from 'core_reportbuilder/local/repository/conditions';\n\n/**\n * Reload conditions settings region\n *\n * @param {Element} reportElement\n * @param {Object} templateContext\n * @return {Promise}\n */\nconst reloadSettingsConditionsRegion = (reportElement, templateContext) => {\n    const pendingPromise = new Pending('core_reportbuilder/conditions:reload');\n    const settingsConditionsRegion = reportElement.querySelector(reportSelectors.regions.settingsConditions);\n\n    return Templates.renderForPromise('core_reportbuilder/local/settings/conditions', {conditions: templateContext})\n        .then(({html, js}) => {\n            const conditionsjs = $.parseHTML(templateContext.javascript, null, true).map(node => node.innerHTML).join(\"\\n\");\n            Templates.replaceNode(settingsConditionsRegion, html, js + conditionsjs);\n\n            initConditionsForm();\n\n            // Re-focus the add condition element after reloading the region.\n            const reportAddCondition = reportElement.querySelector(reportSelectors.actions.reportAddCondition);\n            reportAddCondition?.focus();\n\n            return pendingPromise.resolve();\n        });\n};\n\n/**\n * Initialise conditions form, must be called on each init because the form container is re-created when switching editor modes\n */\nconst initConditionsForm = () => {\n    const reportElement = document.querySelector(reportSelectors.regions.report);\n\n    // Enhance condition selector.\n    const reportAddCondition = reportElement.querySelector(reportSelectors.actions.reportAddCondition);\n    AutoComplete.enhanceField(reportAddCondition, false, '', getString('selectacondition', 'core_reportbuilder'))\n        .catch(Notification.exception);\n\n    // Handle dynamic conditions form.\n    const conditionFormContainer = reportElement.querySelector(reportSelectors.regions.settingsConditions);\n    if (!conditionFormContainer) {\n        return;\n    }\n    const conditionForm = new DynamicForm(conditionFormContainer, '\\\\core_reportbuilder\\\\form\\\\condition');\n\n    // Submit report conditions.\n    conditionForm.addEventListener(conditionForm.events.FORM_SUBMITTED, event => {\n        event.preventDefault();\n\n        getString('conditionsapplied', 'core_reportbuilder')\n            .then(addToast)\n            .catch(Notification.exception);\n\n        // After the form has been submitted, we should trigger report table reload.\n        dispatchEvent(reportEvents.tableReload, {}, reportElement);\n    });\n\n    // Reset report conditions.\n    conditionForm.addEventListener(conditionForm.events.NOSUBMIT_BUTTON_PRESSED, event => {\n        event.preventDefault();\n\n        Notification.saveCancelPromise(\n            getString('resetconditions', 'core_reportbuilder'),\n            getString('resetconditionsconfirm', 'core_reportbuilder'),\n            getString('resetall', 'core_reportbuilder'),\n            {triggerElement: event.detail}\n        ).then(() => {\n            const pendingPromise = new Pending('core_reportbuilder/conditions:reset');\n\n            return resetConditions(reportElement.dataset.reportId)\n                .then(data => reloadSettingsConditionsRegion(reportElement, data))\n                .then(() => addToast(getString('conditionsreset', 'core_reportbuilder')))\n                .then(() => {\n                    dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }).catch(() => {\n            return;\n        });\n    });\n};\n\n/**\n * Initialise module, prefetch all required strings\n *\n * @param {Boolean} initialized Ensure we only add our listeners once\n */\nexport const init = initialized => {\n    prefetchStrings('core_reportbuilder', [\n        'conditionadded',\n        'conditiondeleted',\n        'conditionmoved',\n        'conditionsapplied',\n        'conditionsreset',\n        'deletecondition',\n        'deleteconditionconfirm',\n        'resetall',\n        'resetconditions',\n        'resetconditionsconfirm',\n        'selectacondition',\n    ]);\n\n    prefetchStrings('core', [\n        'delete',\n    ]);\n\n    initConditionsForm();\n    if (initialized) {\n        return;\n    }\n\n    // Add condition to report.\n    document.addEventListener('change', event => {\n        const reportAddCondition = event.target.closest(reportSelectors.actions.reportAddCondition);\n        if (reportAddCondition) {\n            event.preventDefault();\n\n            // Check if dropdown is closed with no condition selected.\n            if (reportAddCondition.value === \"\" || reportAddCondition.value === \"0\") {\n                return;\n            }\n\n            const reportElement = reportAddCondition.closest(reportSelectors.regions.report);\n            const pendingPromise = new Pending('core_reportbuilder/conditions:add');\n\n            addCondition(reportElement.dataset.reportId, reportAddCondition.value)\n                .then(data => reloadSettingsConditionsRegion(reportElement, data))\n                .then(() => getString('conditionadded', 'core_reportbuilder',\n                    reportAddCondition.options[reportAddCondition.selectedIndex].text))\n                .then(addToast)\n                .then(() => {\n                    dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n    });\n\n    document.addEventListener('click', event => {\n\n        // Remove condition from report.\n        const reportRemoveCondition = event.target.closest(reportSelectors.actions.reportRemoveCondition);\n        if (reportRemoveCondition) {\n            event.preventDefault();\n\n            const reportElement = reportRemoveCondition.closest(reportSelectors.regions.report);\n            const conditionContainer = reportRemoveCondition.closest(reportSelectors.regions.activeCondition);\n            const conditionName = conditionContainer.dataset.conditionName;\n\n            Notification.saveCancelPromise(\n                getString('deletecondition', 'core_reportbuilder', conditionName),\n                getString('deleteconditionconfirm', 'core_reportbuilder', conditionName),\n                getString('delete', 'core'),\n                {triggerElement: reportRemoveCondition}\n            ).then(() => {\n                const pendingPromise = new Pending('core_reportbuilder/conditions:remove');\n\n                return deleteCondition(reportElement.dataset.reportId, conditionContainer.dataset.conditionId)\n                    .then(data => reloadSettingsConditionsRegion(reportElement, data))\n                    .then(() => addToast(getString('conditiondeleted', 'core_reportbuilder', conditionName)))\n                    .then(() => {\n                        dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                        return pendingPromise.resolve();\n                    })\n                    .catch(Notification.exception);\n            }).catch(() => {\n                return;\n            });\n        }\n    });\n\n    // Initialize sortable list to handle active conditions moving (note JQuery dependency, see MDL-72293 for resolution).\n    var activeConditionsSortableList = new SortableList(`${reportSelectors.regions.activeConditions}`,\n        {isHorizontal: false});\n    activeConditionsSortableList.getElementName = element => Promise.resolve(element.data('conditionName'));\n\n    $(document).on(SortableList.EVENTS.DROP, reportSelectors.regions.activeCondition, (event, info) => {\n        if (info.positionChanged) {\n            const pendingPromise = new Pending('core_reportbuilder/conditions:reorder');\n            const reportElement = event.target.closest(reportSelectors.regions.report);\n            const conditionId = info.element.data('conditionId');\n            const conditionPosition = info.element.data('conditionPosition');\n\n            // Select target position, if moving to the end then count number of element siblings.\n            let targetConditionPosition = info.targetNextElement.data('conditionPosition') || info.element.siblings().length + 2;\n            if (targetConditionPosition > conditionPosition) {\n                targetConditionPosition--;\n            }\n\n            // Re-order condition, giving drop event transition time to finish.\n            const reorderPromise = reorderCondition(reportElement.dataset.reportId, conditionId, targetConditionPosition);\n            Promise.all([reorderPromise, new Promise(resolve => setTimeout(resolve, 1000))])\n                .then(([data]) => reloadSettingsConditionsRegion(reportElement, data))\n                .then(() => getString('conditionmoved', 'core_reportbuilder', info.element.data('conditionName')))\n                .then(addToast)\n                .then(() => {\n                    dispatchEvent(reportEvents.tableReload, {}, reportElement);\n                    return pendingPromise.resolve();\n                })\n                .catch(Notification.exception);\n        }\n    });\n};\n"], "names": ["reloadSettingsConditionsRegion", "reportElement", "templateContext", "pendingPromise", "Pending", "settingsConditionsRegion", "querySelector", "reportSelectors", "regions", "settingsConditions", "Templates", "renderForPromise", "conditions", "then", "_ref", "html", "js", "<PERSON>js", "$", "parseHTML", "javascript", "map", "node", "innerHTML", "join", "replaceNode", "initConditionsForm", "reportAddCondition", "actions", "focus", "resolve", "document", "report", "enhanceField", "catch", "Notification", "exception", "conditionFormContainer", "conditionForm", "DynamicForm", "addEventListener", "events", "FORM_SUBMITTED", "event", "preventDefault", "addToast", "reportEvents", "tableReload", "NOSUBMIT_BUTTON_PRESSED", "saveCancelPromise", "triggerElement", "detail", "dataset", "reportId", "data", "initialized", "target", "closest", "value", "options", "selectedIndex", "text", "reportRemoveCondition", "conditionC<PERSON><PERSON>", "activeCondition", "conditionName", "conditionId", "SortableList", "activeConditions", "isHorizontal", "getElementName", "element", "Promise", "on", "EVENTS", "DROP", "info", "positionChanged", "conditionPosition", "targetConditionPosition", "targetNextElement", "siblings", "length", "reorderPromise", "all", "setTimeout", "_ref2"], "mappings": "koEAgDMA,+BAAiC,CAACC,cAAeC,yBAC7CC,eAAiB,IAAIC,iBAAQ,wCAC7BC,yBAA2BJ,cAAcK,cAAcC,gBAAgBC,QAAQC,2BAE9EC,mBAAUC,iBAAiB,+CAAgD,CAACC,WAAYV,kBAC1FW,MAAKC,WAACC,KAACA,KAADC,GAAOA,eACJC,aAAeC,gBAAEC,UAAUjB,gBAAgBkB,WAAY,MAAM,GAAMC,KAAIC,MAAQA,KAAKC,YAAWC,KAAK,yBAChGC,YAAYpB,yBAA0BU,KAAMC,GAAKC,cAE3DS,2BAGMC,mBAAqB1B,cAAcK,cAAcC,gBAAgBqB,QAAQD,2BAC/EA,MAAAA,oBAAAA,mBAAoBE,QAEb1B,eAAe2B,cAO5BJ,mBAAqB,WACjBzB,cAAgB8B,SAASzB,cAAcC,gBAAgBC,QAAQwB,QAG/DL,mBAAqB1B,cAAcK,cAAcC,gBAAgBqB,QAAQD,8CAClEM,aAAaN,oBAAoB,EAAO,IAAI,kBAAU,mBAAoB,uBAClFO,MAAMC,sBAAaC,iBAGlBC,uBAAyBpC,cAAcK,cAAcC,gBAAgBC,QAAQC,wBAC9E4B,oCAGCC,cAAgB,IAAIC,qBAAYF,uBAAwB,yCAG9DC,cAAcE,iBAAiBF,cAAcG,OAAOC,gBAAgBC,QAChEA,MAAMC,oCAEI,oBAAqB,sBAC1B/B,KAAKgC,YACLX,MAAMC,sBAAaC,+CAGVU,aAAaC,YAAa,GAAI9C,kBAIhDqC,cAAcE,iBAAiBF,cAAcG,OAAOO,yBAAyBL,QACzEA,MAAMC,uCAEOK,mBACT,kBAAU,kBAAmB,uBAC7B,kBAAU,yBAA0B,uBACpC,kBAAU,WAAY,sBACtB,CAACC,eAAgBP,MAAMQ,SACzBtC,MAAK,WACGV,eAAiB,IAAIC,iBAAQ,8CAE5B,+BAAgBH,cAAcmD,QAAQC,UACxCxC,MAAKyC,MAAQtD,+BAA+BC,cAAeqD,QAC3DzC,MAAK,KAAM,eAAS,kBAAU,kBAAmB,yBACjDA,MAAK,yCACYiC,aAAaC,YAAa,GAAI9C,eACrCE,eAAe2B,aAEzBI,MAAMC,sBAAaC,cACzBF,OAAM,2BAWGqB,6CACA,qBAAsB,CAClC,iBACA,mBACA,iBACA,oBACA,kBACA,kBACA,yBACA,WACA,kBACA,yBACA,mDAGY,OAAQ,CACpB,WAGJ7B,qBACI6B,eAKJxB,SAASS,iBAAiB,UAAUG,cAC1BhB,mBAAqBgB,MAAMa,OAAOC,QAAQlD,gBAAgBqB,QAAQD,uBACpEA,mBAAoB,IACpBgB,MAAMC,iBAG2B,KAA7BjB,mBAAmB+B,OAA6C,MAA7B/B,mBAAmB+B,mBAIpDzD,cAAgB0B,mBAAmB8B,QAAQlD,gBAAgBC,QAAQwB,QACnE7B,eAAiB,IAAIC,iBAAQ,kEAEtBH,cAAcmD,QAAQC,SAAU1B,mBAAmB+B,OAC3D7C,MAAKyC,MAAQtD,+BAA+BC,cAAeqD,QAC3DzC,MAAK,KAAM,kBAAU,iBAAkB,qBACpCc,mBAAmBgC,QAAQhC,mBAAmBiC,eAAeC,QAChEhD,KAAKgC,YACLhC,MAAK,yCACYiC,aAAaC,YAAa,GAAI9C,eACrCE,eAAe2B,aAEzBI,MAAMC,sBAAaC,eAIhCL,SAASS,iBAAiB,SAASG,cAGzBmB,sBAAwBnB,MAAMa,OAAOC,QAAQlD,gBAAgBqB,QAAQkC,0BACvEA,sBAAuB,CACvBnB,MAAMC,uBAEA3C,cAAgB6D,sBAAsBL,QAAQlD,gBAAgBC,QAAQwB,QACtE+B,mBAAqBD,sBAAsBL,QAAQlD,gBAAgBC,QAAQwD,iBAC3EC,cAAgBF,mBAAmBX,QAAQa,oCAEpChB,mBACT,kBAAU,kBAAmB,qBAAsBgB,gBACnD,kBAAU,yBAA0B,qBAAsBA,gBAC1D,kBAAU,SAAU,QACpB,CAACf,eAAgBY,wBACnBjD,MAAK,WACGV,eAAiB,IAAIC,iBAAQ,+CAE5B,+BAAgBH,cAAcmD,QAAQC,SAAUU,mBAAmBX,QAAQc,aAC7ErD,MAAKyC,MAAQtD,+BAA+BC,cAAeqD,QAC3DzC,MAAK,KAAM,eAAS,kBAAU,mBAAoB,qBAAsBoD,kBACxEpD,MAAK,yCACYiC,aAAaC,YAAa,GAAI9C,eACrCE,eAAe2B,aAEzBI,MAAMC,sBAAaC,cACzBF,OAAM,aAOkB,IAAIiC,iCAAgB5D,gBAAgBC,QAAQ4D,kBAC3E,CAACC,cAAc,IACUC,eAAiBC,SAAWC,QAAQ1C,QAAQyC,QAAQjB,KAAK,sCAEpFvB,UAAU0C,GAAGN,uBAAaO,OAAOC,KAAMpE,gBAAgBC,QAAQwD,iBAAiB,CAACrB,MAAOiC,WAClFA,KAAKC,gBAAiB,OAChB1E,eAAiB,IAAIC,iBAAQ,yCAC7BH,cAAgB0C,MAAMa,OAAOC,QAAQlD,gBAAgBC,QAAQwB,QAC7DkC,YAAcU,KAAKL,QAAQjB,KAAK,eAChCwB,kBAAoBF,KAAKL,QAAQjB,KAAK,yBAGxCyB,wBAA0BH,KAAKI,kBAAkB1B,KAAK,sBAAwBsB,KAAKL,QAAQU,WAAWC,OAAS,EAC/GH,wBAA0BD,mBAC1BC,gCAIEI,gBAAiB,gCAAiBlF,cAAcmD,QAAQC,SAAUa,YAAaa,yBACrFP,QAAQY,IAAI,CAACD,eAAgB,IAAIX,SAAQ1C,SAAWuD,WAAWvD,QAAS,SACnEjB,MAAKyE,YAAEhC,mBAAUtD,+BAA+BC,cAAeqD,SAC/DzC,MAAK,KAAM,kBAAU,iBAAkB,qBAAsB+D,KAAKL,QAAQjB,KAAK,oBAC/EzC,KAAKgC,YACLhC,MAAK,yCACYiC,aAAaC,YAAa,GAAI9C,eACrCE,eAAe2B,aAEzBI,MAAMC,sBAAaC"}