define("core_reportbuilder/local/editor/sorting",["exports","jquery","core/inplace_editable","core/notification","core/pending","core/pubsub","core/sortable_list","core/str","core/toast","core_reportbuilder/local/selectors","core_reportbuilder/local/repository/sorting","core/templates","core/event_dispatcher","core_reportbuilder/local/events"],(function(_exports,_jquery,_inplace_editable,_notification,_pending,_pubsub,_sortable_list,_str,_toast,reportSelectors,_sorting,_templates,_event_dispatcher,reportEvents){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_notification=_interopRequireDefault(_notification),_pending=_interopRequireDefault(_pending),_sortable_list=_interopRequireDefault(_sortable_list),reportSelectors=_interopRequireWildcard(reportSelectors),_templates=_interopRequireDefault(_templates),reportEvents=_interopRequireWildcard(reportEvents);const SORTORDER_ASCENDING=4,SORTORDER_DESCENDING=3,reloadSettingsSortingRegion=context=>{const pendingPromise=new _pending.default("core_reportbuilder/sorting:reload"),settingsSortingRegion=document.querySelector(reportSelectors.regions.settingsSorting);return _templates.default.renderForPromise("core_reportbuilder/local/settings/sorting",{sorting:context}).then((_ref=>{let{html:html,js:js}=_ref;return _templates.default.replaceNode(settingsSortingRegion,html,js),pendingPromise.resolve()}))},updateSorting=(reportElement,element,sortenabled,sortdirection)=>{const reportId=reportElement.dataset.reportId,listElement=element.closest("li"),columnId=listElement.dataset.columnSortId,columnName=listElement.dataset.columnSortName;return(0,_sorting.toggleColumnSorting)(reportId,columnId,sortenabled,sortdirection).then(reloadSettingsSortingRegion).then((()=>(0,_str.getString)("columnsortupdated","core_reportbuilder",columnName))).then(_toast.add).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement),null)))};_exports.init=initialized=>{initialized||((0,_pubsub.subscribe)(reportEvents.publish.reportColumnsUpdated,(data=>reloadSettingsSortingRegion(data).catch(_notification.default.exception))),document.addEventListener("click",(event=>{const toggleSorting=event.target.closest(reportSelectors.actions.reportToggleColumnSort);if(toggleSorting){event.preventDefault();const pendingPromise=new _pending.default("core_reportbuilder/sorting:toggle"),reportElement=toggleSorting.closest(reportSelectors.regions.report),sortdirection=parseInt(toggleSorting.closest("li").dataset.columnSortDirection);updateSorting(reportElement,toggleSorting,toggleSorting.checked,sortdirection).then((()=>{const toggleSortingElement=document.getElementById(toggleSorting.id);return null==toggleSortingElement||toggleSortingElement.focus(),pendingPromise.resolve()})).catch(_notification.default.exception)}const toggleSortDirection=event.target.closest(reportSelectors.actions.reportToggleColumnSortDirection);if(toggleSortDirection){event.preventDefault();const pendingPromise=new _pending.default("core_reportbuilder/sorting:direction"),reportElement=toggleSortDirection.closest(reportSelectors.regions.report),listElement=toggleSortDirection.closest("li"),toggleSorting=listElement.querySelector(reportSelectors.actions.reportToggleColumnSort);let sortdirection=parseInt(listElement.dataset.columnSortDirection);sortdirection===SORTORDER_ASCENDING?sortdirection=SORTORDER_DESCENDING:sortdirection===SORTORDER_DESCENDING&&(sortdirection=SORTORDER_ASCENDING),updateSorting(reportElement,toggleSortDirection,toggleSorting.checked,sortdirection).then((()=>{const toggleSortDirectionElement=document.getElementById(toggleSortDirection.id);return null==toggleSortDirectionElement||toggleSortDirectionElement.focus(),pendingPromise.resolve()})).catch(_notification.default.exception)}})),new _sortable_list.default("".concat(reportSelectors.regions.settingsSorting," ul"),{isHorizontal:!1}).getElementName=element=>Promise.resolve(element.data("columnSortName")),(0,_jquery.default)(document).on(_sortable_list.default.EVENTS.DROP,"".concat(reportSelectors.regions.report," li[data-column-sort-id]"),((event,info)=>{if(info.positionChanged){const pendingPromise=new _pending.default("core_reportbuilder/sorting:reorder"),reportElement=event.target.closest(reportSelectors.regions.report),columnId=info.element.data("columnSortId"),columnPosition=info.element.data("columnSortPosition");let targetColumnSortPosition=info.targetNextElement.data("columnSortPosition")||info.element.siblings().length+2;targetColumnSortPosition>columnPosition&&targetColumnSortPosition--;const reorderPromise=(0,_sorting.reorderColumnSorting)(reportElement.dataset.reportId,columnId,targetColumnSortPosition);Promise.all([reorderPromise,new Promise((resolve=>setTimeout(resolve,1e3)))]).then((_ref2=>{let[data]=_ref2;return reloadSettingsSortingRegion(data)})).then((()=>(0,_str.getString)("columnsortupdated","core_reportbuilder",info.element.data("columnSortName")))).then(_toast.add).then((()=>((0,_event_dispatcher.dispatchEvent)(reportEvents.tableReload,{},reportElement),pendingPromise.resolve()))).catch(_notification.default.exception)}})))}}));

//# sourceMappingURL=sorting.min.js.map