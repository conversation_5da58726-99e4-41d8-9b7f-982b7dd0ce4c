<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

declare(strict_types=1);

namespace core_reportbuilder\exception;

use moodle_exception;

/**
 * Unavailable report source exception
 *
 * @package     core_reportbuilder
 * @copyright   2020 Paul Holden <<EMAIL>>
 * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class source_unavailable_exception extends moodle_exception {

    /**
     * Constructor
     *
     * @param string $source
     */
    public function __construct(string $source) {
        parent::__construct('errorsourceunavailable', 'reportbuilder', '', null, $source);
    }
}
