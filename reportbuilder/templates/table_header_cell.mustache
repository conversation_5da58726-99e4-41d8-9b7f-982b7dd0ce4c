{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/table_header_cell

    This template renders the header of the columns

    Example context (json):
    {
        "entityname": "Stuff",
        "name": "Demo",
        "headingeditable": "Demo",
        "movetitle": "Move column 'Demo'"
    }
}}
<div class="d-flex justify-content-start">
    <div class="me-2">
        {{>core/drag_handle}}
    </div>
    <div class="w-100">
        <div class="d-flex justify-content-between mb-2">
            <small class="text-muted text-nowrap text-uppercase">{{entityname}} &bull; {{name}}</small>
            <span class="text-nowrap">
                <button class="btn btn-link p-0"
                        data-action="report-remove-column"
                        type="button"
                        title="{{#str}}deletecolumn, core_reportbuilder, {{{name}}}{{/str}}"
                        aria-label="{{#str}}deletecolumn, core_reportbuilder, {{{name}}}{{/str}}">
                    {{#pix}}e/cancel, core{{/pix}}
                </button>
            </span>
        </div>
        <h3 class="h5 text-nowrap">{{{headingeditable}}}</h3>
        <small class="text-muted text-nowrap">{{{aggregationeditable}}}</small>
    </div>
</div>
