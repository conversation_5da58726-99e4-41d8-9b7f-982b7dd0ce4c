{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/toggle_card

    Template for empty_message

    Example context (json):
    {
        "collapsed": "collapsed",
        "id": "htmlid",
        "header": "Toggle card heading",
        "buttontitle": "Expand toggle card",
        "body": "This is the toggle card content"
    }
}}
<div class="card reportbuilder-toggle-card mb-2">
    <div class="card-header p-0">
        <div class="d-flex align-items-center mb-0 p-1">
            <div class="ms-3">
                <span class="me-1">{{$header}}{{/header}}</span> {{$helpicon}}{{/helpicon}}
            </div>
            <button class="btn toggle-card-button ms-auto {{$collapsed}}{{/collapsed}}"
                    data-toggle="collapse"
                    data-target="#{{$id}}{{uniqid}}{{/id}}"
                    aria-expanded="true"
                    aria-controls="{{$id}}{{uniqid}}{{/id}}"
                    title="{{#str}}showhide, core_reportbuilder, {{$header}}{{/header}}{{/str}}"
            >
                <span class="collapsed-icon-container"><i class="toggle-card-icon fa fa-lg fa-angle-down"></i></span>
                <span class="expanded-icon-container"><i class="toggle-card-icon fa fa-lg fa-angle-up"></i></span>
            </button>
        </div>
    </div>
    <div id="{{$id}}{{uniqid}}{{/id}}" class="collapse {{$collapsed}}show{{/collapsed}}">
        <div class="card-body p-0">
            {{$body}}{{/body}}
        </div>
    </div>
</div>
