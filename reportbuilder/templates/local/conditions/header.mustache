{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_reportbuilder/local/conditions/header

    Template for the header of a condition instance within the condition form

    Example context (json):
    {
        "id": 1,
        "heading": "Date modified",
        "entityname": "Course"
    }
}}
<div class="condition list-group-item list-group-item-action text-dark" data-region="active-condition"
        data-condition-id="{{id}}" data-condition-name="{{heading}}" data-condition-position="{{sortorder}}">
    <div class="condition-header d-flex align-items-start">
        {{>core/drag_handle}}
        <div>
            <div class="d-flex">
                <small class="text-muted text-uppercase">{{entityname}} • {{heading}}</small>
            </div>
        </div>
        <button class="btn btn-link p-0 ms-auto"
                type="button"
                data-action="report-remove-condition"
                title="{{#str}}deletecondition, core_reportbuilder, {{{heading}}}{{/str}}"
                aria-label="{{#str}}deletecondition, core_reportbuilder, {{{heading}}}{{/str}}">
            {{#pix}}e/cancel, core{{/pix}}
        </button>
    </div>
