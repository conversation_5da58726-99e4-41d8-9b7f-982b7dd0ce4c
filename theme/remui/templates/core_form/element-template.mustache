{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

    Edwiser RemUI
    @package theme_remui
    @copyright (c) 2023 WisdmLabs (https://wisdmlabs.com/)
    @license http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
}}
{{!
    @template core_form/element-template

    Template for the form element wrapper template.

    Context variables required for this template:
    * label
    * required
    * advanced
    * helpbutton
    * error
    * element
        * id
        * name

    Example context (json):
    {
        "label": "Course full name",
        "required": true,
        "advanced": false,
        "error": null,
        "element": {
            "wrapperid": "fitem_id_fullname",
            "id": "id_fullname",
            "name": "fullname"
        }
    }
}}
<div id="{{element.wrapperid}}" class="form-group row {{#error}}has-danger{{/error}} fitem {{#element.emptylabel}}femptylabel{{/element.emptylabel}} {{#advanced}}advanced{{/advanced}} {{{element.extraclasses}}}" {{#element.groupname}}data-groupname="{{.}}"{{/element.groupname}}>
    <div class="col-lg-3 col-md-4 col-form-label p-0">
        <div class="d-flex align-items-center flex-gap-1 inner">
            {{# label}}{{$ label }}
                {{^element.staticlabel}}
                    <label class="edw-form-label d-inline word-break m-0 h-100  {{#element.hiddenlabel}}sr-only{{/element.hiddenlabel}}" for="{{element.id}}">
                        {{{label}}}
                    </label>
                {{/element.staticlabel}}
                {{#element.staticlabel}}
                    <span class="d-inline-block {{#element.hiddenlabel}}sr-only{{/element.hiddenlabel}}">
                        {{{label}}}
                    </span>
                {{/element.staticlabel}}
            {{/ label }}{{/ label}}
            <div class="form-label-addon d-flex align-items-center h-100 flex-gap-1">
                {{#required}}
                    <div class="text-danger" title="{{#str}}required{{/str}}">
                        *
                    </div>
                {{/required}}
            </div>
        </div>
    </div>
    <div class="col-12 form-inline align-items-center felement p-0" data-fieldtype="{{element.type}}">
        {{$ element }}
            <!-- Element goes here -->
        {{/ element }}
        <div class="form-control-feedback invalid-feedback" id="{{element.iderror}}" {{#error}} style="display: block;"{{/error}}>
            {{{error}}}
        </div>
    </div>
</div>
{{#js}}
require(['theme_boost/form-display-errors'], function(module) {
    module.enhance({{#quote}}{{element.id}}{{/quote}});
});
{{/js}}
