<?php

// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * This script delegates file serving to individual plugins
 *
 * @package    core
 * @subpackage file
 * @copyright  2008 Petr Skoda (http://skodak.org)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

// Disable moodle specific debug messages and any errors in output.
if (!defined('NO_DEBUG_DISPLAY')) {
    define('NO_DEBUG_DISPLAY', true);
}

require_once('config.php');
require_once('lib/filelib.php');
if (empty($relativepath)) {
    $relativepath = get_file_argument();
}

global $CFG;

// Check if this is a content file that should require authentication
if (!empty($relativepath)) {
    $pathparts = explode('/', ltrim($relativepath, '/'));
    if (count($pathparts) >= 3) {
        $component = $pathparts[1]; // e.g., 'mod_hvp', 'mod_scorm'
        
        // Require authentication for content files from modules
        if (strpos($component, 'mod_') === 0 && isset($CFG->cdndomain) && $CFG->cdndomain) {
            if (empty($_SERVER['HTTP_X_REAL_IP']) || !in_array(trim($_SERVER['HTTP_X_REAL_IP']), array_map('trim', explode(',', $CFG->cdnhost)))) { 
                require_login();
            }
        }
    }
}
$forcedownload = optional_param('forcedownload', 0, PARAM_BOOL);
$preview = optional_param('preview', null, PARAM_ALPHANUM);
// Offline means download the file from the repository and serve it, even if it was an external link.
// The repository may have to export the file to an offline format.
$offline = optional_param('offline', 0, PARAM_BOOL);
$embed = optional_param('embed', 0, PARAM_BOOL);

// We need to set the page URL here because there are cases where file_pluginfile may display an
// error directly on the page instead of throwing an exception, and this will generate a server
// error if $PAGE->url is not defined.
$PAGE->set_url(new \moodle_url($FULLME));

file_pluginfile($relativepath, $forcedownload, $preview, $offline, $embed);
